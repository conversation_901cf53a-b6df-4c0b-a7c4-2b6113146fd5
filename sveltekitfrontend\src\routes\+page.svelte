<script lang="ts">
	import { page } from '$app/stores';
	import { useAvailableLogsQuery, useGlobalAnalyticsQuery } from '$lib/api';
	import { subDays, startOfDay } from 'date-fns';

	let channel = $state<string | null>(null);
	let user = $state<string | null>(null);
	let logSearchText = $state('');

	$effect(() => {
		channel = $page.url.searchParams.get('channel');
		user = $page.url.searchParams.get('username');
	});

	// Get quick analytics for dashboard when no filters
	const to = startOfDay(new Date());
	const from = subDays(to, 7);
	const quickAnalytics = $derived(useGlobalAnalyticsQuery(from, to));

	const availableLogsQuery = $derived(useAvailableLogsQuery(channel, user));

	// For now, let's simplify this to avoid the store subscription issues
	// We'll just show the dashboard when no filters are applied
	const hasFilters = $derived(channel || user);
</script>

<div>
	{#if !hasFilters}
		<div class="dashboard-home">
			<div class="welcome-section">
				<h1>🚀 Welcome to RustLog Dashboard</h1>
				<p>Your comprehensive chat log analysis and monitoring platform</p>
			</div>

			<!-- Quick Stats -->
			{#if $quickAnalytics.data}
				<div class="quick-stats">
					<h2>📊 Quick Stats (Last 7 Days)</h2>
					<div class="stats-grid">
						<div class="stat-card">
							<div class="stat-icon">💬</div>
							<div class="stat-content">
								<span class="stat-value">{$quickAnalytics.data.totalMessages.toLocaleString()}</span>
								<span class="stat-label">Messages</span>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">👤</div>
							<div class="stat-content">
								<span class="stat-value">{$quickAnalytics.data.totalUsers.toLocaleString()}</span>
								<span class="stat-label">Active Users</span>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">📺</div>
							<div class="stat-content">
								<span class="stat-value">{$quickAnalytics.data.totalChannels.toLocaleString()}</span>
								<span class="stat-label">Channels</span>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">📈</div>
							<div class="stat-content">
								<span class="stat-value">{($quickAnalytics.data.totalMessages / 7).toFixed(0)}</span>
								<span class="stat-label">Avg/Day</span>
							</div>
						</div>
					</div>
				</div>
			{/if}

			<!-- Quick Actions -->
			<div class="quick-actions">
				<h2>🎯 Quick Actions</h2>
				<div class="actions-grid">
					<a href="/search" class="action-card">
						<div class="action-icon">🔍</div>
						<div class="action-content">
							<h3>Global Search</h3>
							<p>Search across all channels and users with advanced filters</p>
						</div>
					</a>
					<a href="/analytics" class="action-card">
						<div class="action-icon">📊</div>
						<div class="action-content">
							<h3>Analytics Dashboard</h3>
							<p>View detailed insights and engagement metrics</p>
						</div>
					</a>
					<a href="/tracker" class="action-card">
						<div class="action-icon">👥</div>
						<div class="action-content">
							<h3>User Tracker</h3>
							<p>Monitor specific users across all channels</p>
						</div>
					</a>
				</div>
			</div>

			<!-- Getting Started -->
			<div class="getting-started">
				<h2>🚀 Getting Started</h2>
				<div class="steps">
					<div class="step">
						<div class="step-number">1</div>
						<div class="step-content">
							<h4>Filter by Channel or User</h4>
							<p>Use the filters above to view logs for specific channels or users</p>
						</div>
					</div>
					<div class="step">
						<div class="step-number">2</div>
						<div class="step-content">
							<h4>Explore Global Search</h4>
							<p>Search across all channels with advanced filtering and sorting options</p>
						</div>
					</div>
					<div class="step">
						<div class="step-number">3</div>
						<div class="step-content">
							<h4>Analyze with Analytics</h4>
							<p>View comprehensive analytics and insights about chat activity</p>
						</div>
					</div>
					<div class="step">
						<div class="step-number">4</div>
						<div class="step-content">
							<h4>Track Specific Users</h4>
							<p>Monitor individual users and their activity patterns</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	{:else if $availableLogsQuery.isPending}
		<p>Loading available logs...</p>
	{:else if $availableLogsQuery.isError}
		<p class="error">Error loading logs: {$availableLogsQuery.error.message}</p>
	{:else if !$availableLogsQuery.data || $availableLogsQuery.data.length === 0}
		<p>No logs found for this user in this channel.</p>
	{:else}
		<div class="log-container">
			<div class="log-controls">
				<h3>Logs for {user} in #{channel}</h3>
				<input type="search" placeholder="Search loaded logs..." bind:value={logSearchText} />
			</div>

			<div class="list-container">
				<p>Log display functionality will be implemented after fixing the reactive query issues.</p>
				<p>Available logs: {$availableLogsQuery.data.length}</p>
			</div>
		</div>
	{/if}
</div>

<style>
	.dashboard-home {
		max-width: 1200px;
		margin: 0 auto;
		padding: var(--space-8) var(--space-4);
		min-height: 100vh;
	}

	.welcome-section {
		text-align: center;
		padding: var(--space-16) var(--space-8);
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		border-radius: var(--radius-2xl);
		margin-bottom: var(--space-12);
		box-shadow: var(--shadow-xl);
		position: relative;
		overflow: hidden;
	}

	.welcome-section::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
		pointer-events: none;
	}

	.welcome-section h1 {
		margin: 0 0 var(--space-4) 0;
		font-size: var(--font-size-5xl);
		font-weight: 700;
		position: relative;
		z-index: 1;
	}

	.welcome-section p {
		margin: 0;
		font-size: var(--font-size-xl);
		opacity: 0.95;
		position: relative;
		z-index: 1;
		font-weight: 400;
	}

	.quick-stats {
		margin-bottom: var(--space-12);
	}

	.quick-stats h2 {
		margin: 0 0 var(--space-8) 0;
		color: var(--text);
		font-size: var(--font-size-3xl);
		text-align: center;
		font-weight: 600;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: var(--space-6);
	}

	.stat-card {
		display: flex;
		align-items: center;
		gap: var(--space-4);
		padding: var(--space-6);
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
		box-shadow: var(--shadow);
	}

	.stat-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 4px;
		height: 100%;
		background: linear-gradient(to bottom, var(--theme2), var(--theme2-bright));
		transition: width 0.3s ease;
	}

	.stat-card:hover {
		transform: translateY(-4px);
		box-shadow: var(--shadow-lg);
		border-color: var(--border-light);
	}

	.stat-card:hover::before {
		width: 6px;
	}

	.stat-icon {
		font-size: 3rem;
		opacity: 0.9;
		filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
	}

	.stat-content {
		display: flex;
		flex-direction: column;
		flex: 1;
	}

	.stat-value {
		font-size: var(--font-size-3xl);
		font-weight: 700;
		color: var(--text);
		line-height: var(--leading-tight);
		margin-bottom: var(--space-1);
	}

	.stat-label {
		font-size: var(--font-size-sm);
		color: var(--text-secondary);
		font-weight: 500;
		text-transform: uppercase;
		letter-spacing: 0.025em;
	}

	.quick-actions {
		margin-bottom: var(--space-12);
	}

	.quick-actions h2 {
		margin: 0 0 var(--space-8) 0;
		color: var(--text);
		font-size: var(--font-size-3xl);
		text-align: center;
		font-weight: 600;
	}

	.actions-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
		gap: var(--space-6);
	}

	.action-card {
		display: flex;
		align-items: center;
		gap: var(--space-4);
		padding: var(--space-6);
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		text-decoration: none;
		color: var(--text);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
		box-shadow: var(--shadow);
	}

	.action-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(135deg, rgba(var(--theme2-rgb), 0.05) 0%, transparent 50%);
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	.action-card:hover {
		transform: translateY(-6px);
		box-shadow: var(--shadow-xl);
		border-color: var(--theme2);
	}

	.action-card:hover::before {
		opacity: 1;
	}

	.action-icon {
		font-size: 3rem;
		opacity: 0.9;
		transition: transform 0.3s ease;
		filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
		z-index: 1;
		position: relative;
	}

	.action-card:hover .action-icon {
		transform: scale(1.1);
	}

	.action-content {
		flex: 1;
		z-index: 1;
		position: relative;
	}

	.action-content h3 {
		margin: 0 0 var(--space-2) 0;
		color: var(--text);
		font-size: var(--font-size-xl);
		font-weight: 600;
		transition: color 0.3s ease;
	}

	.action-card:hover .action-content h3 {
		color: var(--theme2);
	}

	.action-content p {
		margin: 0;
		color: var(--text-secondary);
		font-size: var(--font-size-sm);
		line-height: var(--leading-relaxed);
	}

	.getting-started h2 {
		margin: 0 0 var(--space-8) 0;
		color: var(--text);
		font-size: var(--font-size-3xl);
		text-align: center;
		font-weight: 600;
	}

	.steps {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: var(--space-6);
	}

	.step {
		display: flex;
		gap: var(--space-4);
		padding: var(--space-6);
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		transition: all 0.3s ease;
		box-shadow: var(--shadow);
		position: relative;
		overflow: hidden;
	}

	.step::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 3px;
		background: linear-gradient(90deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		transform: scaleX(0);
		transition: transform 0.3s ease;
	}

	.step:hover {
		transform: translateY(-2px);
		box-shadow: var(--shadow-md);
		border-color: var(--border-light);
	}

	.step:hover::before {
		transform: scaleX(1);
	}

	.step-number {
		width: 3rem;
		height: 3rem;
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		border-radius: var(--radius-full);
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 700;
		font-size: var(--font-size-lg);
		flex-shrink: 0;
		box-shadow: var(--shadow);
		transition: transform 0.3s ease;
	}

	.step:hover .step-number {
		transform: scale(1.1);
	}

	.step-content h4 {
		margin: 0 0 var(--space-2) 0;
		color: var(--text);
		font-size: var(--font-size-lg);
		font-weight: 600;
	}

	.step-content p {
		margin: 0;
		color: var(--text-secondary);
		font-size: var(--font-size-sm);
		line-height: var(--leading-relaxed);
	}

	.error {
		color: var(--danger);
		padding: var(--space-4);
		background: rgba(var(--danger-rgb), 0.1);
		border: 1px solid rgba(var(--danger-rgb), 0.2);
		border-radius: var(--radius-md);
		font-weight: 500;
	}

	.log-container {
		display: flex;
		flex-direction: column;
		height: 80vh;
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		padding: var(--space-6);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
	}

	.log-controls {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: var(--space-4);
		flex-shrink: 0;
		gap: var(--space-4);
	}

	.log-controls h3 {
		margin: 0;
		color: var(--text);
		font-size: var(--font-size-xl);
		font-weight: 600;
	}

	.log-controls input {
		max-width: 300px;
		flex: 1;
	}

	.list-container {
		flex-grow: 1;
		background: var(--bg-dark);
		border-radius: var(--radius-lg);
		overflow: hidden;
		position: relative;
		border: 1px solid var(--border-color);
		padding: var(--space-4);
		display: flex;
		align-items: center;
		justify-content: center;
		color: var(--text-secondary);
		font-size: var(--font-size-sm);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.dashboard-home {
			padding: var(--space-4) var(--space-2);
		}

		.welcome-section {
			padding: var(--space-12) var(--space-4);
		}

		.welcome-section h1 {
			font-size: var(--font-size-3xl);
		}

		.welcome-section p {
			font-size: var(--font-size-lg);
		}

		.stats-grid,
		.actions-grid,
		.steps {
			grid-template-columns: 1fr;
		}

		.stat-card,
		.action-card {
			padding: var(--space-4);
		}

		.log-controls {
			flex-direction: column;
			align-items: stretch;
		}

		.log-controls input {
			max-width: none;
		}
	}

	@media (max-width: 480px) {
		.welcome-section h1 {
			font-size: var(--font-size-2xl);
		}

		.stat-card,
		.action-card,
		.step {
			flex-direction: column;
			text-align: center;
			gap: var(--space-3);
		}

		.stat-icon,
		.action-icon {
			font-size: 2.5rem;
		}
	}
</style>