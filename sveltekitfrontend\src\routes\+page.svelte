<script lang="ts">
	import { page } from '$app/stores';
	import { useAvailableLogsQuery, useGlobalAnalyticsQuery } from '$lib/api';
	import { subDays, startOfDay } from 'date-fns';

	let channel = $state<string | null>(null);
	let user = $state<string | null>(null);
	let logSearchText = $state('');

	$effect(() => {
		channel = $page.url.searchParams.get('channel');
		user = $page.url.searchParams.get('username');
	});

	// Get quick analytics for dashboard when no filters
	const to = startOfDay(new Date());
	const from = subDays(to, 7);
	const quickAnalytics = $derived(useGlobalAnalyticsQuery(from, to));

	const availableLogsQuery = $derived(useAvailableLogsQuery(channel, user));

	// For now, let's simplify this to avoid the store subscription issues
	// We'll just show the dashboard when no filters are applied
	const hasFilters = $derived(channel || user);
</script>

<div>
	{#if !hasFilters}
		<div class="dashboard-home">
			<div class="welcome-section">
				<h1>🚀 Welcome to RustLog Dashboard</h1>
				<p>Your comprehensive chat log analysis and monitoring platform</p>
			</div>

			<!-- Quick Stats -->
			{#if $quickAnalytics.data}
				<div class="quick-stats">
					<h2>📊 Quick Stats (Last 7 Days)</h2>
					<div class="stats-grid">
						<div class="stat-card">
							<div class="stat-icon">💬</div>
							<div class="stat-content">
								<span class="stat-value">{$quickAnalytics.data.totalMessages.toLocaleString()}</span>
								<span class="stat-label">Messages</span>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">👤</div>
							<div class="stat-content">
								<span class="stat-value">{$quickAnalytics.data.totalUsers.toLocaleString()}</span>
								<span class="stat-label">Active Users</span>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">📺</div>
							<div class="stat-content">
								<span class="stat-value">{$quickAnalytics.data.totalChannels.toLocaleString()}</span>
								<span class="stat-label">Channels</span>
							</div>
						</div>
						<div class="stat-card">
							<div class="stat-icon">📈</div>
							<div class="stat-content">
								<span class="stat-value">{($quickAnalytics.data.totalMessages / 7).toFixed(0)}</span>
								<span class="stat-label">Avg/Day</span>
							</div>
						</div>
					</div>
				</div>
			{/if}

			<!-- Quick Actions -->
			<div class="quick-actions">
				<h2>🎯 Quick Actions</h2>
				<div class="actions-grid">
					<a href="/search" class="action-card">
						<div class="action-icon">🔍</div>
						<div class="action-content">
							<h3>Global Search</h3>
							<p>Search across all channels and users with advanced filters</p>
						</div>
					</a>
					<a href="/analytics" class="action-card">
						<div class="action-icon">📊</div>
						<div class="action-content">
							<h3>Analytics Dashboard</h3>
							<p>View detailed insights and engagement metrics</p>
						</div>
					</a>
					<a href="/tracker" class="action-card">
						<div class="action-icon">👥</div>
						<div class="action-content">
							<h3>User Tracker</h3>
							<p>Monitor specific users across all channels</p>
						</div>
					</a>
				</div>
			</div>

			<!-- Getting Started -->
			<div class="getting-started">
				<h2>🚀 Getting Started</h2>
				<div class="steps">
					<div class="step">
						<div class="step-number">1</div>
						<div class="step-content">
							<h4>Filter by Channel or User</h4>
							<p>Use the filters above to view logs for specific channels or users</p>
						</div>
					</div>
					<div class="step">
						<div class="step-number">2</div>
						<div class="step-content">
							<h4>Explore Global Search</h4>
							<p>Search across all channels with advanced filtering and sorting options</p>
						</div>
					</div>
					<div class="step">
						<div class="step-number">3</div>
						<div class="step-content">
							<h4>Analyze with Analytics</h4>
							<p>View comprehensive analytics and insights about chat activity</p>
						</div>
					</div>
					<div class="step">
						<div class="step-number">4</div>
						<div class="step-content">
							<h4>Track Specific Users</h4>
							<p>Monitor individual users and their activity patterns</p>
						</div>
					</div>
				</div>
			</div>
		</div>
	{:else if $availableLogsQuery.isPending}
		<p>Loading available logs...</p>
	{:else if $availableLogsQuery.isError}
		<p class="error">Error loading logs: {$availableLogsQuery.error.message}</p>
	{:else if !$availableLogsQuery.data || $availableLogsQuery.data.length === 0}
		<p>No logs found for this user in this channel.</p>
	{:else}
		<div class="log-container">
			<div class="log-controls">
				<h3>Logs for {user} in #{channel}</h3>
				<input type="search" placeholder="Search loaded logs..." bind:value={logSearchText} />
			</div>

			<div class="list-container">
				<p>Log display functionality will be implemented after fixing the reactive query issues.</p>
				<p>Available logs: {$availableLogsQuery.data.length}</p>
			</div>
		</div>
	{/if}
</div>

<style>
	.dashboard-home {
		max-width: 1200px;
		margin: 0 auto;
		padding: 2rem 1rem;
	}

	.welcome-section {
		text-align: center;
		padding: 3rem 1rem;
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		border-radius: 16px;
		margin-bottom: 3rem;
	}

	.welcome-section h1 {
		margin: 0 0 1rem 0;
		font-size: 3rem;
		font-weight: 700;
	}

	.welcome-section p {
		margin: 0;
		font-size: 1.25rem;
		opacity: 0.9;
	}

	.quick-stats {
		margin-bottom: 3rem;
	}

	.quick-stats h2 {
		margin: 0 0 1.5rem 0;
		color: var(--theme2);
		font-size: 1.75rem;
		text-align: center;
	}

	.stats-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
		gap: 1.5rem;
	}

	.stat-card {
		display: flex;
		align-items: center;
		gap: 1rem;
		padding: 1.5rem;
		background: var(--bg-bright);
		border-radius: 12px;
		border: 1px solid var(--border-color);
		transition: transform 0.2s, box-shadow 0.2s;
	}

	.stat-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	.stat-icon {
		font-size: 2.5rem;
		opacity: 0.8;
	}

	.stat-content {
		display: flex;
		flex-direction: column;
	}

	.stat-value {
		font-size: 2rem;
		font-weight: 700;
		color: var(--theme2);
		line-height: 1;
	}

	.stat-label {
		font-size: 0.875rem;
		color: var(--text-dark);
		margin-top: 0.25rem;
	}

	.quick-actions {
		margin-bottom: 3rem;
	}

	.quick-actions h2 {
		margin: 0 0 1.5rem 0;
		color: var(--theme2);
		font-size: 1.75rem;
		text-align: center;
	}

	.actions-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: 1.5rem;
	}

	.action-card {
		display: flex;
		align-items: center;
		gap: 1rem;
		padding: 1.5rem;
		background: var(--bg-bright);
		border-radius: 12px;
		border: 1px solid var(--border-color);
		text-decoration: none;
		color: var(--text);
		transition: all 0.2s;
	}

	.action-card:hover {
		transform: translateY(-2px);
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		border-color: var(--theme2);
	}

	.action-icon {
		font-size: 2.5rem;
		opacity: 0.8;
	}

	.action-content h3 {
		margin: 0 0 0.5rem 0;
		color: var(--theme2);
		font-size: 1.25rem;
	}

	.action-content p {
		margin: 0;
		color: var(--text-dark);
		font-size: 0.9rem;
	}

	.getting-started h2 {
		margin: 0 0 1.5rem 0;
		color: var(--theme2);
		font-size: 1.75rem;
		text-align: center;
	}

	.steps {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
		gap: 1.5rem;
	}

	.step {
		display: flex;
		gap: 1rem;
		padding: 1.5rem;
		background: var(--bg-bright);
		border-radius: 12px;
		border: 1px solid var(--border-color);
	}

	.step-number {
		width: 2.5rem;
		height: 2.5rem;
		background: var(--theme2);
		color: white;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		font-weight: 700;
		font-size: 1.1rem;
		flex-shrink: 0;
	}

	.step-content h4 {
		margin: 0 0 0.5rem 0;
		color: var(--text);
		font-size: 1.1rem;
	}

	.step-content p {
		margin: 0;
		color: var(--text-dark);
		font-size: 0.9rem;
		line-height: 1.5;
	}

	.error {
		color: var(--danger);
	}

	.log-container {
		display: flex;
		flex-direction: column;
		height: 80vh;
		background: var(--bg-bright);
		border-radius: 8px;
		padding: 1rem;
	}

	.log-controls {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 1rem;
		flex-shrink: 0;
	}

	.list-container {
		flex-grow: 1;
		background: var(--bg-dark);
		border-radius: 4px;
		overflow: hidden;
		position: relative;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.welcome-section h1 {
			font-size: 2rem;
		}

		.welcome-section p {
			font-size: 1rem;
		}

		.stats-grid,
		.actions-grid,
		.steps {
			grid-template-columns: 1fr;
		}

		.dashboard-home {
			padding: 1rem;
		}
	}
</style>