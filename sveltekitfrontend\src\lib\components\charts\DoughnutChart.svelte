<script lang="ts">
	import Chart from 'chart.js/auto';

	let { data, title }: { data: any[]; title: string } = $props();
	let canvas: HTMLCanvasElement;
	let chart: Chart;

	const chartConfig = $derived.by(() => {
		// Handle different data structures
		const labels = data.map((d) => {
			if (d.message_type) {
				return d.message_type.charAt(0).toUpperCase() + d.message_type.slice(1);
			} else if (d.label) {
				return d.label;
			} else if (d.name) {
				return d.name;
			} else {
				return 'Unknown';
			}
		});

		const values = data.map((d) => {
			if (d.count !== undefined) {
				return d.count;
			} else if (d.value !== undefined) {
				return d.value;
			} else if (d.y !== undefined) {
				return d.y;
			} else {
				return 0;
			}
		});

		return {
			type: 'doughnut' as const,
			data: {
				labels,
				datasets: [
					{
						data: values,
					backgroundColor: [
						'rgba(41, 128, 185, 0.8)',
						'rgba(0, 204, 102, 0.8)',
						'rgba(230, 126, 34, 0.8)',
						'rgba(231, 76, 60, 0.8)',
						'rgba(142, 68, 173, 0.8)',
						'rgba(127, 140, 141, 0.8)'
					],
					borderColor: '#18181b',
					borderWidth: 2
				}
			]
		},
		options: {
			responsive: true,
			maintainAspectRatio: false,
			plugins: {
				title: { display: true, text: title, color: '#f5f5f5' },
				legend: { position: 'bottom' as const, labels: { color: '#f5f5f5' } },
				tooltip: {
					callbacks: {
						label: (context: any) => {
							const item = data[context.dataIndex];
							return `${context.label}: ${context.formattedValue} (${item.percentage.toFixed(1)}%)`;
						}
					}
				}
			}
		}
	};
});

	$effect(() => {
		if (chart) chart.destroy();
		chart = new Chart(canvas, chartConfig);
		return () => chart?.destroy();
	});
</script>

<canvas bind:this={canvas}></canvas>