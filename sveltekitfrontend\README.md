# RustLog SvelteKit Frontend

A modern, responsive frontend for the RustLog chat analysis platform built with SvelteKit 5, featuring comprehensive analytics, search capabilities, and user tracking.

## 🚀 Features

### 🎨 Modern Design System

- **Dark Theme**: Carefully crafted dark color scheme optimized for readability
- **Typography**: Inter font family with consistent sizing and spacing scales
- **Components**: Reusable UI components with hover effects and animations
- **Responsive**: Mobile-first design that works on all screen sizes
- **Accessibility**: Proper ARIA labels, keyboard navigation, and focus management

### 🔍 Global Search

- **Advanced Search**: Comprehensive search with multiple filter options
- **Real-time Results**: Live search results with pagination
- **Channel Filtering**: Filter by specific channels or search across all
- **Date Range**: Search within specific time periods
- **Message Types**: Filter by different message types (chat, action, etc.)
- **User Filtering**: Search messages from specific users
- **Export Options**: Export search results for further analysis

### 📊 Analytics Dashboard

- **Interactive Charts**: Beautiful visualizations using Chart.js
- **Multiple Views**: Switch between different analytics perspectives
- **Time Range Selection**: Analyze data across different time periods
- **Key Metrics**: Total messages, active users, channel statistics
- **Channel Breakdown**: Detailed per-channel analytics
- **User Activity**: Top users and activity patterns
- **Message Trends**: Temporal analysis of chat activity

### 👥 User Tracker

- **Individual Tracking**: Add and track specific users
- **Activity Metrics**: Comprehensive user activity statistics
- **Channel Distribution**: See which channels users are most active in
- **Message History**: Browse user's message history with search
- **Activity Timeline**: Visual representation of user activity
- **Comparative Analysis**: Compare multiple users side by side

### 🏠 Dashboard Home

- **Quick Stats**: Overview of key platform metrics
- **Recent Activity**: Latest platform activity and trends
- **Quick Actions**: Fast access to common tasks
- **Getting Started**: Helpful guide for new users
- **Navigation**: Intuitive navigation to all platform features

## 🛠 Technology Stack

- **SvelteKit 5**: Latest version with runes and modern reactivity
- **TypeScript**: Full type safety throughout the application
- **TanStack Query**: Powerful data fetching and caching
- **Chart.js**: Interactive and responsive charts
- **CSS Custom Properties**: Modern CSS with design tokens
- **Vite**: Fast development and optimized builds

## 📁 Project Structure

```
sveltekitfrontend/
├── src/
│   ├── lib/
│   │   ├── components/          # Reusable UI components
│   │   │   ├── charts/         # Chart components
│   │   │   ├── Filters.svelte  # Navigation and filters
│   │   │   └── SearchResults.svelte
│   │   ├── api/                # API integration layer
│   │   └── utils/              # Utility functions
│   ├── routes/                 # SvelteKit routes
│   │   ├── +layout.svelte      # Root layout
│   │   ├── +page.svelte        # Dashboard home
│   │   ├── search/             # Global search
│   │   ├── analytics/          # Analytics dashboard
│   │   └── tracker/            # User tracker
│   ├── app.css                 # Global styles and design system
│   └── app.html                # HTML template
├── static/                     # Static assets
└── package.json               # Dependencies and scripts
```

## 🎨 Design System

### Color Palette

- **Primary**: Modern teal gradient (#00d4aa to #00f5c4)
- **Secondary**: Purple gradient (#667eea to #764ba2)
- **Background**: Dark grays (#0a0a0b to #2a2a2e)
- **Text**: High contrast whites and grays
- **Status**: Success, warning, danger, and info colors

### Typography Scale

- **Font Family**: Inter (Google Fonts)
- **Sizes**: xs (0.75rem) to 5xl (3rem)
- **Weights**: 300, 400, 500, 600, 700
- **Line Heights**: Tight, normal, relaxed, loose

### Spacing Scale

- **Base Unit**: 0.25rem (4px)
- **Scale**: 1-20 (0.25rem to 5rem)
- **Consistent**: Used throughout for margins, padding, gaps

### Component Variants

- **Cards**: Default, hover states, selected states
- **Buttons**: Primary, secondary, danger variants
- **Badges**: Status indicators with color coding
- **Forms**: Consistent input styling with focus states

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm, yarn, or pnpm
- RustLog backend running on localhost:8025

### Installation

1. **Clone and navigate to the frontend directory**

   ```bash
   cd sveltekitfrontend
   ```

2. **Install dependencies**

   ```bash
   npm install
   # or
   yarn install
   # or
   pnpm install
   ```

3. **Start the development server**

   ```bash
   npm run dev
   # or
   yarn dev
   # or
   pnpm dev
   ```

4. **Open your browser**
   Navigate to `http://localhost:5173`

### Production Build

```bash
npm run build
npm run preview
```

## 🔧 Configuration

### Environment Variables

Create a `.env` file in the root directory:

```env
# API Configuration
VITE_API_BASE_URL=http://localhost:8025
VITE_API_TIMEOUT=30000

# Feature Flags
VITE_ENABLE_ANALYTICS=true
VITE_ENABLE_USER_TRACKER=true
VITE_ENABLE_EXPORT=true
```

### API Integration

The frontend communicates with the RustLog backend through a RESTful API. All API calls are handled through the TanStack Query integration for optimal caching and error handling.

## 📱 Responsive Design

The application is fully responsive and optimized for:

- **Desktop**: Full feature set with multi-column layouts
- **Tablet**: Adapted layouts with touch-friendly interactions
- **Mobile**: Single-column layouts with collapsible navigation

## ♿ Accessibility

- **Keyboard Navigation**: Full keyboard support for all interactions
- **Screen Readers**: Proper ARIA labels and semantic HTML
- **Color Contrast**: WCAG AA compliant color combinations
- **Focus Management**: Clear focus indicators and logical tab order

## 🧪 Testing

### Manual Testing Checklist

- [ ] Navigation between all pages works correctly
- [ ] Search functionality returns expected results
- [ ] Analytics charts render and update properly
- [ ] User tracker can add/remove users and display data
- [ ] Responsive design works on different screen sizes
- [ ] All forms validate input correctly
- [ ] Error states display appropriate messages

### Automated Testing

```bash
# Run component tests
npm run test

# Run e2e tests
npm run test:e2e
```

## 🤝 Contributing

1. Follow the existing code style and patterns
2. Use TypeScript for all new code
3. Add proper error handling and loading states
4. Test on multiple screen sizes
5. Ensure accessibility compliance
6. Update documentation for new features

## 📄 License

This project is part of the RustLog platform. See the main project license for details.

## 🔗 Related Projects

- [RustLog Backend](../README.md) - The Rust backend API
- [Original Frontend](../web/) - The previous frontend implementation

---

Built with ❤️ using SvelteKit 5 and modern web technologies.
