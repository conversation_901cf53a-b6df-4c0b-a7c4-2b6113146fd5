<script lang="ts">
	import type { FullLogMessage } from '$lib/types';
	import LogLine from './LogLine.svelte';
	import { createEventDispatcher } from 'svelte';

	interface Props {
		results: FullLogMessage[] | undefined;
		isLoading: boolean;
		error: any;
		searchQuery?: string;
		isGlobalSearch?: boolean;
	}

	let {
		results,
		isLoading,
		error,
		searchQuery = '',
		isGlobalSearch = false
	}: Props = $props();

	const dispatch = createEventDispatcher<{
		clearSearch: void;
		export: void;
	}>();

	let currentPage = $state(1);
	let sortBy = $state<'timestamp' | 'channel' | 'user'>('timestamp');
	let sortOrder = $state<'asc' | 'desc'>('desc');
	let selectedChannels = $state<string[]>([]);
	let selectedUsers = $state<string[]>([]);
	let showFilters = $state(false);

	const ITEMS_PER_PAGE = 50;

	// Extract unique channels and users from results
	const availableChannels = $derived(() => {
		if (!results) return [];
		return [...new Set(results.map((msg) => msg.channel))].sort();
	});

	const availableUsers = $derived(() => {
		if (!results) return [];
		return [...new Set(results.map((msg) => msg.username || msg.displayName))].sort();
	});

	// Filter results based on selected channels and users
	const filteredResults = $derived(() => {
		if (!results) return [];

		let filtered = results;

		if (selectedChannels.length > 0) {
			filtered = filtered.filter((msg) => selectedChannels.includes(msg.channel));
		}

		if (selectedUsers.length > 0) {
			filtered = filtered.filter(
				(msg) =>
					selectedUsers.includes(msg.username) || selectedUsers.includes(msg.displayName)
			);
		}

		return filtered;
	});

	// Sort filtered results
	const sortedResults = $derived(() => {
		return [...filteredResults].sort((a, b) => {
			if (sortBy === 'timestamp') {
				const timeA = new Date(a.timestamp).getTime();
				const timeB = new Date(b.timestamp).getTime();
				return sortOrder === 'asc' ? timeA - timeB : timeB - timeA;
			} else if (sortBy === 'channel') {
				const channelA = a.channel.toLowerCase();
				const channelB = b.channel.toLowerCase();
				return sortOrder === 'asc'
					? channelA.localeCompare(channelB)
					: channelB.localeCompare(channelA);
			} else if (sortBy === 'user') {
				const userA = (a.username || a.displayName).toLowerCase();
				const userB = (b.username || b.displayName).toLowerCase();
				return sortOrder === 'asc' ? userA.localeCompare(userB) : userB.localeCompare(userA);
			}
			return 0;
		});
	});

	// Paginate results
	const paginatedResults = $derived(() => {
		const startIndex = (currentPage - 1) * ITEMS_PER_PAGE;
		const endIndex = startIndex + ITEMS_PER_PAGE;
		return sortedResults.slice(startIndex, endIndex);
	});

	const totalPages = $derived(() => Math.ceil(sortedResults.length / ITEMS_PER_PAGE));

	// Channel and user statistics
	const channelStats = $derived(() => {
		const stats = new Map<string, number>();
		filteredResults.forEach((msg) => {
			stats.set(msg.channel, (stats.get(msg.channel) || 0) + 1);
		});
		return Array.from(stats.entries())
			.map(([channel, count]) => ({ channel, count }))
			.sort((a, b) => b.count - a.count);
	});

	const userStats = $derived(() => {
		const stats = new Map<string, number>();
		filteredResults.forEach((msg) => {
			const user = msg.username || msg.displayName;
			stats.set(user, (stats.get(user) || 0) + 1);
		});
		return Array.from(stats.entries())
			.map(([user, count]) => ({ user, count }))
			.sort((a, b) => b.count - a.count);
	});

	function handleExport() {
		if (results) {
			const dataStr = JSON.stringify(results, null, 2);
			const dataBlob = new Blob([dataStr], { type: 'application/json' });
			const url = URL.createObjectURL(dataBlob);
			const link = document.createElement('a');
			link.href = url;
			link.download = `search-results-${new Date().toISOString().split('T')[0]}.json`;
			link.click();
			URL.revokeObjectURL(url);
		}
	}

	function toggleChannelFilter(channel: string) {
		if (selectedChannels.includes(channel)) {
			selectedChannels = selectedChannels.filter((c) => c !== channel);
		} else {
			selectedChannels = [...selectedChannels, channel];
		}
		currentPage = 1; // Reset to first page when filtering
	}

	function toggleUserFilter(user: string) {
		if (selectedUsers.includes(user)) {
			selectedUsers = selectedUsers.filter((u) => u !== user);
		} else {
			selectedUsers = [...selectedUsers, user];
		}
		currentPage = 1; // Reset to first page when filtering
	}

	function clearAllFilters() {
		selectedChannels = [];
		selectedUsers = [];
		currentPage = 1;
	}

	// Reset page when results change
	$effect(() => {
		if (results) {
			currentPage = 1;
		}
	});
</script>

{#if isLoading}
	<div class="loading">
		<div class="spinner"></div>
		<p>Searching...</p>
	</div>
{:else if error}
	<div class="error-container">
		<div class="error">Search failed: {error.message || 'Unknown error'}</div>
	</div>
{:else if !results || results.length === 0}
	<div class="no-results">
		<p>No results found for "{searchQuery}"</p>
	</div>
{:else}
	<div class="search-results">
		<!-- Results Header -->
		<div class="results-header">
			<div class="results-stats">
				<h3>Search Results</h3>
				<div class="stats-chips">
					<span class="chip primary">{filteredResults.length} results</span>
					{#if filteredResults.length !== results.length}
						<span class="chip secondary">{results.length} total</span>
					{/if}
					{#if isGlobalSearch}
						<span class="chip">{channelStats.length} channels</span>
						<span class="chip">{userStats.length} users</span>
					{/if}
				</div>
			</div>

			<div class="results-controls">
				<select bind:value={sortBy}>
					<option value="timestamp">Sort by Time</option>
					{#if isGlobalSearch}
						<option value="channel">Sort by Channel</option>
						<option value="user">Sort by User</option>
					{/if}
				</select>

				<button
					class="sort-order"
					onclick={() => (sortOrder = sortOrder === 'asc' ? 'desc' : 'asc')}
					title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
				>
					{sortOrder === 'asc' ? '↑' : '↓'}
				</button>

				{#if isGlobalSearch}
					<button
						class="filter-toggle"
						class:active={showFilters}
						onclick={() => (showFilters = !showFilters)}
					>
						Filters
					</button>
				{/if}

				<button onclick={handleExport}>Export</button>
				<button onclick={() => dispatch('clearSearch')}>Clear</button>
			</div>
		</div>

		<!-- Filters Panel -->
		{#if isGlobalSearch && showFilters}
			<div class="filters-panel">
				<h4>Filters & Analytics</h4>

				<div class="filter-section">
					<h5>Top Channels</h5>
					<div class="filter-chips">
						{#each channelStats.slice(0, 8) as { channel, count }}
							<button
								class="filter-chip"
								class:active={selectedChannels.includes(channel)}
								onclick={() => toggleChannelFilter(channel)}
							>
								#{channel} ({count})
							</button>
						{/each}
					</div>
				</div>

				<div class="filter-section">
					<h5>Top Users</h5>
					<div class="filter-chips">
						{#each userStats.slice(0, 8) as { user, count }}
							<button
								class="filter-chip"
								class:active={selectedUsers.includes(user)}
								onclick={() => toggleUserFilter(user)}
							>
								{user} ({count})
							</button>
						{/each}
					</div>
				</div>

				{#if selectedChannels.length > 0 || selectedUsers.length > 0}
					<button class="clear-filters" onclick={clearAllFilters}>Clear All Filters</button>
				{/if}
			</div>
		{/if}

		<!-- Results List -->
		<div class="results-list">
			{#each paginatedResults as message, index (message.id || index)}
				<div class="result-item">
					{#if isGlobalSearch}
						<div class="message-meta">
							<span class="channel-chip">#{message.channel}</span>
							<span class="user-chip">{message.username || message.displayName}</span>
							<span class="timestamp">{new Date(message.timestamp).toLocaleString()}</span>
						</div>
					{/if}
					<LogLine {message} />
				</div>
			{/each}
		</div>

		<!-- Pagination -->
		{#if totalPages > 1}
			<div class="pagination">
				<button disabled={currentPage === 1} onclick={() => (currentPage = 1)}>First</button>
				<button disabled={currentPage === 1} onclick={() => currentPage--}>Previous</button>
				<span>Page {currentPage} of {totalPages}</span>
				<button disabled={currentPage === totalPages} onclick={() => currentPage++}>Next</button>
				<button disabled={currentPage === totalPages} onclick={() => (currentPage = totalPages)}>
					Last
				</button>
			</div>
		{/if}
	</div>
{/if}

<style>
	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: var(--space-12);
		gap: var(--space-4);
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
	}

	.loading p {
		color: var(--text-secondary);
		font-size: var(--font-size-lg);
		font-weight: 500;
		margin: 0;
	}

	.spinner {
		width: 3rem;
		height: 3rem;
		border: 3px solid var(--border-color);
		border-top: 3px solid var(--theme2);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% {
			transform: rotate(0deg);
		}
		100% {
			transform: rotate(360deg);
		}
	}

	.error-container,
	.no-results {
		padding: var(--space-8);
		text-align: center;
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
	}

	.no-results p {
		color: var(--text-secondary);
		font-size: var(--font-size-lg);
		margin: 0;
	}

	.error {
		color: var(--danger);
		background: rgba(var(--danger-rgb), 0.1);
		padding: var(--space-4);
		border-radius: var(--radius-lg);
		border: 1px solid rgba(var(--danger-rgb), 0.2);
		border-left: 4px solid var(--danger);
		font-weight: 500;
	}

	.search-results {
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		overflow: hidden;
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
	}

	.results-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: var(--space-6);
		border-bottom: 1px solid var(--border-color);
		flex-wrap: wrap;
		gap: var(--space-4);
		background: var(--bg-bright);
	}

	.results-stats {
		display: flex;
		align-items: center;
		gap: var(--space-4);
		flex-wrap: wrap;
	}

	.results-stats h3 {
		margin: 0;
		color: var(--text);
		font-size: var(--font-size-xl);
		font-weight: 600;
	}

	.stats-chips {
		display: flex;
		gap: var(--space-2);
		flex-wrap: wrap;
	}

	.chip {
		padding: var(--space-2) var(--space-3);
		background: var(--bg-dark);
		border: 1px solid var(--border-color);
		border-radius: var(--radius-full);
		font-size: var(--font-size-sm);
		font-weight: 500;
		transition: all 0.2s ease;
	}

	.chip.primary {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		border-color: var(--theme2);
		color: white;
		box-shadow: var(--shadow-sm);
	}

	.chip.secondary {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		border-color: var(--theme);
		color: white;
		box-shadow: var(--shadow-sm);
	}

	.results-controls {
		display: flex;
		gap: var(--space-2);
		align-items: center;
		flex-wrap: wrap;
	}

	.results-controls select {
		padding: var(--space-2) var(--space-3);
		border-radius: var(--radius-md);
		border: 1px solid var(--border-color);
		background: var(--bg-dark);
		font-size: var(--font-size-sm);
	}

	.results-controls button {
		padding: var(--space-2) var(--space-3);
		font-size: var(--font-size-sm);
		border-radius: var(--radius-md);
		transition: all 0.2s ease;
	}

	.sort-order {
		width: 2.5rem;
		height: 2.5rem;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: var(--font-size-lg);
		font-weight: 600;
		padding: 0;
	}

	.filter-toggle.active {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		border-color: var(--theme2);
		color: white;
		box-shadow: var(--shadow-sm);
	}

	.filters-panel {
		padding: var(--space-6);
		background: var(--bg-dark);
		border-bottom: 1px solid var(--border-color);
		animation: slideDown 0.3s ease;
	}

	.filters-panel h4 {
		margin: 0 0 var(--space-4) 0;
		color: var(--text);
		font-size: var(--font-size-lg);
		font-weight: 600;
	}

	.filter-section {
		margin-bottom: var(--space-6);
	}

	.filter-section h5 {
		margin: 0 0 var(--space-3) 0;
		color: var(--text-secondary);
		font-size: var(--font-size-sm);
		text-transform: uppercase;
		letter-spacing: 0.025em;
		font-weight: 600;
	}

	.filter-chips {
		display: flex;
		gap: var(--space-2);
		flex-wrap: wrap;
	}

	.filter-chip {
		padding: var(--space-2) var(--space-3);
		background: var(--bg-bright);
		border: 1px solid var(--border-color);
		border-radius: var(--radius-full);
		font-size: var(--font-size-sm);
		cursor: pointer;
		transition: all 0.3s ease;
		font-weight: 500;
	}

	.filter-chip:hover {
		border-color: var(--theme2);
		background: var(--bg-hover);
		transform: translateY(-1px);
		box-shadow: var(--shadow-sm);
	}

	.filter-chip.active {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		border-color: var(--theme2);
		color: white;
		box-shadow: var(--shadow-sm);
	}

	.clear-filters {
		background: linear-gradient(135deg, var(--danger) 0%, #ff6b6b 100%);
		border-color: var(--danger);
		color: white;
		margin-top: var(--space-3);
		font-weight: 500;
		box-shadow: var(--shadow-sm);
	}

	.clear-filters:hover {
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.results-list {
		max-height: 70vh;
		overflow-y: auto;
		scrollbar-width: thin;
		scrollbar-color: var(--border-color) transparent;
	}

	.results-list::-webkit-scrollbar {
		width: 8px;
	}

	.results-list::-webkit-scrollbar-track {
		background: transparent;
	}

	.results-list::-webkit-scrollbar-thumb {
		background: var(--border-color);
		border-radius: var(--radius-full);
	}

	.results-list::-webkit-scrollbar-thumb:hover {
		background: var(--border-light);
	}

	.result-item {
		padding: var(--space-4) var(--space-6);
		border-bottom: 1px solid var(--border-color);
		transition: all 0.2s ease;
	}

	.result-item:last-child {
		border-bottom: none;
	}

	.result-item:hover {
		background: var(--bg-hover);
		border-left: 4px solid var(--theme2);
		padding-left: calc(var(--space-6) - 4px);
	}

	.message-meta {
		display: flex;
		gap: var(--space-2);
		margin-bottom: var(--space-2);
		align-items: center;
		flex-wrap: wrap;
	}

	.channel-chip,
	.user-chip {
		padding: var(--space-1) var(--space-2);
		border-radius: var(--radius);
		font-size: var(--font-size-xs);
		font-weight: 600;
		text-transform: uppercase;
		letter-spacing: 0.025em;
	}

	.channel-chip {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		box-shadow: var(--shadow-sm);
	}

	.user-chip {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		color: white;
		box-shadow: var(--shadow-sm);
	}

	.timestamp {
		font-size: var(--font-size-xs);
		color: var(--text-muted);
		font-weight: 500;
	}

	.pagination {
		display: flex;
		justify-content: center;
		align-items: center;
		gap: var(--space-2);
		padding: var(--space-6);
		border-top: 1px solid var(--border-color);
		background: var(--bg-bright);
	}

	.pagination button {
		padding: var(--space-2) var(--space-4);
		font-size: var(--font-size-sm);
		border-radius: var(--radius-md);
		transition: all 0.2s ease;
		font-weight: 500;
	}

	.pagination button:disabled {
		opacity: 0.5;
		cursor: not-allowed;
	}

	.pagination button:not(:disabled):hover {
		background: var(--bg-hover);
		transform: translateY(-1px);
		box-shadow: var(--shadow-sm);
	}

	.pagination span {
		font-size: var(--font-size-sm);
		color: var(--text-secondary);
		font-weight: 500;
		padding: 0 var(--space-2);
	}
</style>
