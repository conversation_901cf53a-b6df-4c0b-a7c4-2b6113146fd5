#!/usr/bin/env python3
"""
Test script to verify the Global Search functionality in the SvelteKit frontend.
"""

import requests
import json
from datetime import datetime, timedelta

def test_backend_endpoints():
    """Test the backend endpoints that the frontend uses"""
    base_url = "http://localhost:8025"
    
    print("🔍 Testing Backend Endpoints for Global Search")
    print("=" * 60)
    
    # Test basic global search
    print("\n📊 Testing Basic Global Search:")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/search?q=test&json=1")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Basic search successful")
            print(f"📄 Messages returned: {len(data.get('messages', []))}")
            if data.get('messages'):
                print(f"📄 Sample message: {data['messages'][0].get('text', 'N/A')[:50]}...")
        else:
            print(f"❌ Basic search failed: {response.text}")
    except Exception as e:
        print(f"❌ Basic search error: {e}")
    
    # Test advanced search
    print("\n📊 Testing Advanced Search:")
    print("-" * 40)
    try:
        # Test with date range
        to_date = datetime.now()
        from_date = to_date - timedelta(days=7)
        
        params = {
            'q': 'test',
            'json': '1',
            'from': from_date.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            'to': to_date.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
        }
        response = requests.get(f"{base_url}/advanced-search", params=params)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Advanced search successful")
            print(f"📄 Messages returned: {len(data.get('messages', []))}")
        else:
            print(f"❌ Advanced search failed: {response.text}")
    except Exception as e:
        print(f"❌ Advanced search error: {e}")
    
    # Test channels endpoint
    print("\n📊 Testing Channels Endpoint:")
    print("-" * 40)
    try:
        response = requests.get(f"{base_url}/channels")
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Channels endpoint successful")
            print(f"📄 Channels returned: {len(data.get('channels', []))}")
            if data.get('channels'):
                print(f"📄 Sample channels: {[c.get('name') for c in data['channels'][:3]]}")
        else:
            print(f"❌ Channels endpoint failed: {response.text}")
    except Exception as e:
        print(f"❌ Channels endpoint error: {e}")
    
    # Test analytics endpoint
    print("\n📊 Testing Analytics Endpoint:")
    print("-" * 40)
    try:
        to_date = datetime.now()
        from_date = to_date - timedelta(days=7)
        
        params = {
            'from': from_date.strftime('%Y-%m-%dT%H:%M:%S.%fZ'),
            'to': to_date.strftime('%Y-%m-%dT%H:%M:%S.%fZ')
        }
        response = requests.get(f"{base_url}/analytics", params=params)
        print(f"Status: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Analytics endpoint successful")
            print(f"📄 Total messages: {data.get('totalMessages', 'N/A')}")
            print(f"📄 Total users: {data.get('totalUsers', 'N/A')}")
            print(f"📄 Total channels: {data.get('totalChannels', 'N/A')}")
            print(f"📄 Top users count: {len(data.get('topUsers', []))}")
            print(f"📄 Top channels count: {len(data.get('topChannels', []))}")
        else:
            print(f"❌ Analytics endpoint failed: {response.text}")
    except Exception as e:
        print(f"❌ Analytics endpoint error: {e}")

def test_frontend_accessibility():
    """Test if the frontend is accessible"""
    print("\n🌐 Testing Frontend Accessibility:")
    print("-" * 40)
    try:
        response = requests.get("http://localhost:5173")
        print(f"Frontend status: {response.status_code}")
        if response.status_code == 200:
            print("✅ Frontend is accessible")
        else:
            print("❌ Frontend is not accessible")
    except Exception as e:
        print(f"❌ Frontend accessibility error: {e}")

if __name__ == "__main__":
    test_backend_endpoints()
    test_frontend_accessibility()
    
    print("\n" + "=" * 60)
    print("🎯 Test Summary:")
    print("- Check if all endpoints return 200 status")
    print("- Verify data structure matches frontend expectations")
    print("- Test both basic and advanced search functionality")
    print("- Ensure frontend can connect to backend on localhost:8025")
