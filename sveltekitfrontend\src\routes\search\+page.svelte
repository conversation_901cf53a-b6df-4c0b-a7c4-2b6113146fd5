<script lang="ts">
	import { useAdvancedSearchQuery, useChannelsQuery } from '$lib/api';
	import SearchResults from '$lib/components/SearchResults.svelte';
	import type { AdvancedSearchParams } from '$lib/types';
	import { subDays } from 'date-fns';

	let searchQuery = $state('');
	let useAdvancedMode = $state(false);
	let showAdvancedFields = $state(false);
	let advancedParams = $state<AdvancedSearchParams>({});

	const channelsQuery = useChannelsQuery();

	// Convert form data to search params
	const searchParams = $derived(() => {
		if (useAdvancedMode) {
			// Convert string dates to Date objects for the API
			const params = { ...advancedParams };
			if (params.from && typeof params.from === 'string') {
				params.from = new Date(params.from);
			}
			if (params.to && typeof params.to === 'string') {
				params.to = new Date(params.to);
			}
			return {
				...params,
				q: params.q || undefined
			};
		} else {
			return { q: searchQuery || undefined };
		}
	});

	const searchResultsQuery = useAdvancedSearchQuery(searchParams(), 'global', undefined);

	function handleBasicSearch(event: SubmitEvent) {
		event.preventDefault();
		// Search will trigger automatically via reactive statement
	}

	function handleAdvancedSearch(event: SubmitEvent) {
		event.preventDefault();
		// Search will trigger automatically via reactive statement
	}

	function clearSearch() {
		searchQuery = '';
		advancedParams = {};
		useAdvancedMode = false;
		showAdvancedFields = false;
	}

	function addChannelFilter(channel: string) {
		if (!advancedParams.channels) {
			advancedParams.channels = [];
		}
		if (!advancedParams.channels.includes(channel)) {
			advancedParams.channels = [...advancedParams.channels, channel];
		}
	}

	function removeChannelFilter(channel: string) {
		if (advancedParams.channels) {
			advancedParams.channels = advancedParams.channels.filter(c => c !== channel);
		}
	}

	function addUserFilter(user: string) {
		if (!advancedParams.users) {
			advancedParams.users = [];
		}
		if (!advancedParams.users.includes(user)) {
			advancedParams.users = [...advancedParams.users, user];
		}
	}

	function removeUserFilter(user: string) {
		if (advancedParams.users) {
			advancedParams.users = advancedParams.users.filter(u => u !== user);
		}
	}

	// Set default date range when advanced mode is enabled
	$effect(() => {
		if (useAdvancedMode && !advancedParams.from && !advancedParams.to) {
			const now = new Date();
			const thirtyDaysAgo = subDays(now, 30);
			advancedParams = {
				...advancedParams,
				from: thirtyDaysAgo.toISOString().slice(0, 16) as any,
				to: now.toISOString().slice(0, 16) as any
			};
		}
	});
</script>

<div class="container">
	<div class="search-header">
		<div class="header-content">
			<div class="title-section">
				<h1>🌐 Global Search</h1>
				<p>Search across all logged channels and users</p>
			</div>
			<div class="mode-toggle">
				<label class="switch">
					<input type="checkbox" bind:checked={useAdvancedMode} />
					<span class="slider"></span>
					<span class="label">Advanced Mode</span>
				</label>
			</div>
		</div>
	</div>

	{#if useAdvancedMode}
		<div class="info-panel">
			<p>💡 <strong>Tip:</strong> You can search for specific users by adding them to "User Filters" without entering a search query. A 30-day date range will be applied automatically.</p>
		</div>
	{/if}

	<!-- Search Form -->
	<div class="search-section">
		{#if useAdvancedMode}
			<form class="advanced-search-form" onsubmit={handleAdvancedSearch}>
				<div class="search-input-group">
					<input
						type="search"
						placeholder="Enter search terms (optional)..."
						bind:value={advancedParams.q}
						class="search-input"
					/>
					<button
						type="button"
						class="advanced-toggle"
						class:active={showAdvancedFields}
						onclick={() => (showAdvancedFields = !showAdvancedFields)}
					>
						⚙️ Options
					</button>
				</div>

				{#if showAdvancedFields}
					<div class="advanced-fields">
						<div class="field-group">
							<h4>Search Options</h4>
							<div class="checkbox-group">
								<label>
									<input type="checkbox" bind:checked={advancedParams.regex} />
									Regular Expression
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.caseSensitive} />
									Case Sensitive
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.searchDisplayNames} />
									Search Display Names
								</label>
							</div>
						</div>

						<div class="field-group">
							<h4>Date Range</h4>
							<div class="date-inputs">
								<input
									type="datetime-local"
									bind:value={advancedParams.from}
									placeholder="From"
								/>
								<input
									type="datetime-local"
									bind:value={advancedParams.to}
									placeholder="To"
								/>
							</div>
						</div>

						<div class="field-group">
							<h4>Message Length</h4>
							<div class="number-inputs">
								<input
									type="number"
									bind:value={advancedParams.minLength}
									placeholder="Min length"
									min="0"
								/>
								<input
									type="number"
									bind:value={advancedParams.maxLength}
									placeholder="Max length"
									min="0"
								/>
							</div>
						</div>

						<div class="field-group">
							<h4>User Type Filters</h4>
							<div class="checkbox-group">
								<label>
									<input type="checkbox" bind:checked={advancedParams.subscribersOnly} />
									Subscribers Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.vipsOnly} />
									VIPs Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.moderatorsOnly} />
									Moderators Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.firstMessagesOnly} />
									First Messages Only
								</label>
								<label>
									<input type="checkbox" bind:checked={advancedParams.hasEmotes} />
									Has Emotes
								</label>
							</div>
						</div>
					</div>
				{/if}
			</form>
		{:else}
			<form class="basic-search-form" onsubmit={handleBasicSearch}>
				<div class="search-input-group">
					<input
						type="search"
						placeholder="Search across all channels..."
						bind:value={searchQuery}
						class="search-input"
					/>
					<button type="submit">🔍 Search</button>
				</div>
			</form>
		{/if}
	<!-- Channel Filters -->
	{#if useAdvancedMode && $channelsQuery.data}
		<div class="filters-section">
			<h4>Channel Filters</h4>
			<div class="filter-input-group">
				<select onchange={(e) => addChannelFilter((e.target as HTMLSelectElement).value)}>
					<option value="">Add channel...</option>
					{#each $channelsQuery.data as channel}
						<option value={channel.name}>{channel.name}</option>
					{/each}
				</select>
			</div>
			{#if advancedParams.channels && advancedParams.channels.length > 0}
				<div class="filter-chips">
					{#each advancedParams.channels as channel}
						<span class="filter-chip">
							#{channel}
							<button onclick={() => removeChannelFilter(channel)}>×</button>
						</span>
					{/each}
				</div>
			{/if}
		</div>
	{/if}

	<!-- User Filters -->
	{#if useAdvancedMode}
		<div class="filters-section">
			<h4>User Filters</h4>
			<div class="filter-input-group">
				<input
					type="text"
					placeholder="Add username..."
					onkeydown={(e) => {
						if (e.key === 'Enter') {
							e.preventDefault();
							const input = e.target as HTMLInputElement;
							if (input.value.trim()) {
								addUserFilter(input.value.trim());
								input.value = '';
							}
						}
					}}
				/>
			</div>
			{#if advancedParams.users && advancedParams.users.length > 0}
				<div class="filter-chips">
					{#each advancedParams.users as user}
						<span class="filter-chip">
							{user}
							<button onclick={() => removeUserFilter(user)}>×</button>
						</span>
					{/each}
				</div>
			{/if}
		</div>
	{/if}

	<!-- Search Results -->
	<SearchResults
		results={$searchResultsQuery.data}
		isLoading={$searchResultsQuery.isPending}
		error={$searchResultsQuery.error}
		searchQuery={useAdvancedMode ? advancedParams.q : searchQuery}
		isGlobalSearch={true}
		on:clearSearch={clearSearch}
	/>
</div>

<style>
	.container {
		padding: var(--space-6);
		max-width: 1400px;
		margin: 0 auto;
		min-height: 100vh;
	}

	.search-header {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		padding: var(--space-12) var(--space-8);
		border-radius: var(--radius-2xl);
		margin-bottom: var(--space-8);
		box-shadow: var(--shadow-xl);
		position: relative;
		overflow: hidden;
	}

	.search-header::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
		pointer-events: none;
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: var(--space-4);
		position: relative;
		z-index: 1;
	}

	.title-section h1 {
		margin: 0 0 var(--space-2) 0;
		font-size: var(--font-size-4xl);
		font-weight: 700;
	}

	.title-section p {
		margin: 0;
		opacity: 0.95;
		font-size: var(--font-size-lg);
		font-weight: 400;
	}

	.mode-toggle {
		display: flex;
		align-items: center;
		gap: var(--space-4);
	}

	.switch {
		position: relative;
		display: flex;
		align-items: center;
		gap: var(--space-3);
		cursor: pointer;
	}

	.switch input {
		opacity: 0;
		width: 0;
		height: 0;
	}

	.slider {
		position: relative;
		width: 56px;
		height: 28px;
		background-color: rgba(255, 255, 255, 0.2);
		border-radius: var(--radius-full);
		transition: all 0.3s ease;
		border: 2px solid rgba(255, 255, 255, 0.3);
	}

	.slider:before {
		position: absolute;
		content: '';
		height: 20px;
		width: 20px;
		left: 2px;
		top: 2px;
		background-color: white;
		border-radius: 50%;
		transition: all 0.3s ease;
		box-shadow: var(--shadow-sm);
	}

	.switch input:checked + .slider {
		background-color: var(--theme);
		border-color: var(--theme-bright);
	}

	.switch input:checked + .slider:before {
		transform: translateX(28px);
	}

	.switch .label {
		font-weight: 600;
		font-size: var(--font-size-base);
		text-shadow: 0 1px 2px rgba(0,0,0,0.1);
	}

	.info-panel {
		background: rgba(var(--info-rgb), 0.1);
		border: 1px solid rgba(var(--info-rgb), 0.2);
		border-radius: var(--radius-lg);
		padding: var(--space-4);
		margin-bottom: var(--space-6);
		color: var(--text);
		border-left: 4px solid var(--info);
	}

	.search-section {
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		padding: var(--space-8);
		margin-bottom: var(--space-6);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
		transition: all 0.3s ease;
	}

	.search-section:hover {
		border-color: var(--border-light);
		box-shadow: var(--shadow-md);
	}

	.search-input-group {
		display: flex;
		gap: var(--space-3);
		align-items: center;
		margin-bottom: var(--space-4);
	}

	.search-input {
		flex: 1;
		padding: var(--space-4);
		font-size: var(--font-size-base);
		border: 2px solid var(--border-color);
		border-radius: var(--radius-lg);
		background: var(--bg-dark);
		color: var(--text);
		transition: all 0.3s ease;
		font-weight: 400;
	}

	.search-input:focus {
		outline: none;
		border-color: var(--theme2);
		box-shadow: 0 0 0 3px rgba(var(--theme2-rgb), 0.1);
		background: var(--bg-bright);
	}

	.search-input::placeholder {
		color: var(--text-muted);
	}

	.advanced-toggle {
		padding: var(--space-4);
		background: var(--bg-dark);
		border: 2px solid var(--border-color);
		border-radius: var(--radius-lg);
		color: var(--text);
		cursor: pointer;
		transition: all 0.3s ease;
		font-weight: 500;
		display: flex;
		align-items: center;
		gap: var(--space-2);
		white-space: nowrap;
	}

	.advanced-toggle:hover {
		border-color: var(--theme2);
		background: var(--bg-hover);
		transform: translateY(-1px);
		box-shadow: var(--shadow-sm);
	}

	.advanced-toggle.active {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		border-color: var(--theme2);
		color: white;
		box-shadow: var(--shadow-md);
	}

	.advanced-fields {
		background: var(--bg-dark);
		border-radius: var(--radius-xl);
		padding: var(--space-8);
		margin-top: var(--space-4);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
		animation: slideDown 0.3s ease;
	}

	@keyframes slideDown {
		from {
			opacity: 0;
			transform: translateY(-10px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.field-group {
		margin-bottom: var(--space-6);
	}

	.field-group:last-child {
		margin-bottom: 0;
	}

	.field-group h4 {
		margin: 0 0 var(--space-3) 0;
		color: var(--text);
		font-size: var(--font-size-lg);
		font-weight: 600;
		border-bottom: 2px solid var(--theme2);
		padding-bottom: var(--space-2);
		display: inline-block;
	}

	.checkbox-group {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: var(--space-4);
	}

	.checkbox-group label {
		display: flex;
		align-items: center;
		gap: var(--space-2);
		cursor: pointer;
		font-size: var(--font-size-sm);
		padding: var(--space-2);
		border-radius: var(--radius);
		transition: background-color 0.2s ease;
	}

	.checkbox-group label:hover {
		background: var(--bg-hover);
	}

	.checkbox-group input[type="checkbox"] {
		width: 18px;
		height: 18px;
		accent-color: var(--theme2);
	}

	.date-inputs,
	.number-inputs {
		display: flex;
		gap: var(--space-3);
		flex-wrap: wrap;
	}

	.date-inputs input,
	.number-inputs input {
		flex: 1;
		min-width: 200px;
		padding: var(--space-3);
		border-radius: var(--radius-md);
	}

	.filters-section {
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		padding: var(--space-6);
		margin-bottom: var(--space-4);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
		transition: all 0.3s ease;
	}

	.filters-section:hover {
		border-color: var(--border-light);
		box-shadow: var(--shadow-md);
	}

	.filters-section h4 {
		margin: 0 0 var(--space-4) 0;
		color: var(--text);
		font-size: var(--font-size-lg);
		font-weight: 600;
		border-bottom: 2px solid var(--theme2);
		padding-bottom: var(--space-2);
		display: inline-block;
	}

	.filter-input-group {
		margin-bottom: var(--space-3);
	}

	.filter-input-group select,
	.filter-input-group input {
		width: 100%;
		max-width: 350px;
		padding: var(--space-3);
		border-radius: var(--radius-md);
		border: 2px solid var(--border-color);
		background: var(--bg-dark);
		transition: all 0.3s ease;
	}

	.filter-input-group select:focus,
	.filter-input-group input:focus {
		border-color: var(--theme2);
		box-shadow: 0 0 0 3px rgba(var(--theme2-rgb), 0.1);
		background: var(--bg-bright);
	}

	.filter-chips {
		display: flex;
		flex-wrap: wrap;
		gap: var(--space-2);
	}

	.filter-chip {
		display: flex;
		align-items: center;
		gap: var(--space-2);
		padding: var(--space-2) var(--space-4);
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		border-radius: var(--radius-full);
		font-size: var(--font-size-sm);
		font-weight: 500;
		box-shadow: var(--shadow-sm);
		transition: all 0.3s ease;
		animation: slideIn 0.3s ease;
	}

	@keyframes slideIn {
		from {
			opacity: 0;
			transform: scale(0.8);
		}
		to {
			opacity: 1;
			transform: scale(1);
		}
	}

	.filter-chip:hover {
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.filter-chip button {
		background: none;
		border: none;
		color: white;
		cursor: pointer;
		padding: var(--space-1);
		font-size: var(--font-size-lg);
		line-height: 1;
		opacity: 0.8;
		border-radius: var(--radius-full);
		transition: all 0.2s ease;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 20px;
		height: 20px;
	}

	.filter-chip button:hover {
		opacity: 1;
		background: rgba(255, 255, 255, 0.2);
		transform: scale(1.1);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.container {
			padding: var(--space-4);
		}

		.search-header {
			padding: var(--space-8) var(--space-4);
		}

		.header-content {
			flex-direction: column;
			text-align: center;
		}

		.title-section h1 {
			font-size: var(--font-size-3xl);
		}

		.search-input-group {
			flex-direction: column;
			gap: var(--space-3);
		}

		.checkbox-group {
			grid-template-columns: 1fr;
		}

		.date-inputs,
		.number-inputs {
			flex-direction: column;
		}

		.date-inputs input,
		.number-inputs input {
			min-width: auto;
		}
	}
</style>