:root {
	/* Background Colors */
	--bg: #0a0a0b;
	--bg-bright: #1a1a1d;
	--bg-brighter: #2a2a2e;
	--bg-dark: #0f0f11;
	--bg-card: #16161a;
	--bg-hover: #1f1f23;

	/* Primary Theme Colors */
	--theme: #00d4aa;
	--theme-bright: #00f5c4;
	--theme-dark: #00b894;
	--theme-rgb: 0, 212, 170;

	/* Secondary Theme Colors */
	--theme2: #667eea;
	--theme2-bright: #764ba2;
	--theme2-dark: #5a67d8;
	--theme2-rgb: 102, 126, 234;

	/* Text Colors */
	--text: #ffffff;
	--text-secondary: #a0a0a0;
	--text-muted: #6b7280;
	--text-dark: #4b5563;

	/* Status Colors */
	--danger: #ef4444;
	--danger-rgb: 239, 68, 68;
	--success: #10b981;
	--success-rgb: 16, 185, 129;
	--warning: #f59e0b;
	--warning-rgb: 245, 158, 11;
	--info: #3b82f6;
	--info-rgb: 59, 130, 246;

	/* Border & Shadow */
	--border-color: #374151;
	--border-light: #4b5563;
	--shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
	--shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
	--shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
	--shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
	--shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

	/* Spacing Scale */
	--space-1: 0.25rem;
	--space-2: 0.5rem;
	--space-3: 0.75rem;
	--space-4: 1rem;
	--space-5: 1.25rem;
	--space-6: 1.5rem;
	--space-8: 2rem;
	--space-10: 2.5rem;
	--space-12: 3rem;
	--space-16: 4rem;
	--space-20: 5rem;

	/* Border Radius */
	--radius-sm: 0.125rem;
	--radius: 0.25rem;
	--radius-md: 0.375rem;
	--radius-lg: 0.5rem;
	--radius-xl: 0.75rem;
	--radius-2xl: 1rem;
	--radius-full: 9999px;

	/* Typography */
	--font-size-xs: 0.75rem;
	--font-size-sm: 0.875rem;
	--font-size-base: 1rem;
	--font-size-lg: 1.125rem;
	--font-size-xl: 1.25rem;
	--font-size-2xl: 1.5rem;
	--font-size-3xl: 1.875rem;
	--font-size-4xl: 2.25rem;
	--font-size-5xl: 3rem;

	/* Line Heights */
	--leading-tight: 1.25;
	--leading-snug: 1.375;
	--leading-normal: 1.5;
	--leading-relaxed: 1.625;
	--leading-loose: 2;
}

body {
	margin: 0;
	padding: 0;
	background: var(--bg);
	color: var(--text);
	font-family:
		'Inter',
		-apple-system,
		BlinkMacSystemFont,
		'Segoe UI',
		Roboto,
		Helvetica,
		Arial,
		sans-serif,
		'Apple Color Emoji',
		'Segoe UI Emoji',
		'Segoe UI Symbol';
	height: 100vh;
	width: 100vw;
	line-height: var(--leading-normal);
	font-size: var(--font-size-base);
	font-weight: 400;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
	overflow-x: hidden;
}

* {
	box-sizing: border-box;
}

html {
	scroll-behavior: smooth;
	height: 100%;
}

#app {
	min-height: 100vh;
	display: flex;
	flex-direction: column;
}

/* Typography */
h1,
h2,
h3,
h4,
h5,
h6 {
	margin: 0 0 var(--space-4) 0;
	font-weight: 600;
	line-height: var(--leading-tight);
	color: var(--text);
}

h1 {
	font-size: var(--font-size-4xl);
}
h2 {
	font-size: var(--font-size-3xl);
}
h3 {
	font-size: var(--font-size-2xl);
}
h4 {
	font-size: var(--font-size-xl);
}
h5 {
	font-size: var(--font-size-lg);
}
h6 {
	font-size: var(--font-size-base);
}

p {
	margin: 0 0 var(--space-4) 0;
	color: var(--text-secondary);
	line-height: var(--leading-relaxed);
}

/* Links */
a {
	color: var(--theme2);
	text-decoration: none;
	transition: all 0.2s ease;
	border-radius: var(--radius);
}

a:hover {
	color: var(--theme2-bright);
}

a:focus-visible {
	outline: 2px solid var(--theme2);
	outline-offset: 2px;
}

/* Form Elements */
button,
input,
select,
textarea {
	font-family: inherit;
	font-size: var(--font-size-sm);
	padding: var(--space-3) var(--space-4);
	border: 1px solid var(--border-color);
	background-color: var(--bg-card);
	color: var(--text);
	border-radius: var(--radius-md);
	transition: all 0.2s ease;
	line-height: var(--leading-normal);
}

/* Button Styles */
button {
	cursor: pointer;
	font-weight: 500;
	display: inline-flex;
	align-items: center;
	justify-content: center;
	gap: var(--space-2);
	min-height: 2.5rem;
}

button:hover:not(:disabled) {
	background-color: var(--bg-hover);
	border-color: var(--border-light);
	transform: translateY(-1px);
	box-shadow: var(--shadow-sm);
}

button:active:not(:disabled) {
	transform: translateY(0);
}

button:disabled {
	opacity: 0.5;
	cursor: not-allowed;
}

/* Primary Button */
.btn-primary {
	background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
	border-color: var(--theme2);
	color: white;
	font-weight: 600;
}

.btn-primary:hover:not(:disabled) {
	background: linear-gradient(135deg, var(--theme2-dark) 0%, var(--theme2) 100%);
	border-color: var(--theme2-dark);
	box-shadow: var(--shadow-md);
}

/* Secondary Button */
.btn-secondary {
	background: var(--bg-bright);
	border-color: var(--border-color);
	color: var(--text);
}

.btn-secondary:hover:not(:disabled) {
	background: var(--bg-brighter);
	border-color: var(--border-light);
}

/* Input Focus States */
input:focus,
select:focus,
textarea:focus {
	outline: none;
	border-color: var(--theme2);
	box-shadow: 0 0 0 3px rgba(var(--theme2-rgb), 0.1);
	background-color: var(--bg-bright);
}

input::placeholder,
textarea::placeholder {
	color: var(--text-muted);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
	background: var(--border-color);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--text-dark);
}

/* Selection Styling */
::selection {
	background: rgba(var(--theme2-rgb), 0.3);
	color: var(--text);
}

/* Focus Visible for Accessibility */
:focus-visible {
	outline: 2px solid var(--theme2);
	outline-offset: 2px;
}

/* Card Components */
.card {
	background: var(--bg-card);
	border: 1px solid var(--border-color);
	border-radius: var(--radius-lg);
	padding: var(--space-6);
	box-shadow: var(--shadow);
	transition: all 0.2s ease;
}

.card:hover {
	border-color: var(--border-light);
	box-shadow: var(--shadow-md);
}

.card-header {
	margin-bottom: var(--space-4);
	padding-bottom: var(--space-4);
	border-bottom: 1px solid var(--border-color);
}

.card-title {
	margin: 0;
	font-size: var(--font-size-lg);
	font-weight: 600;
	color: var(--text);
}

.card-description {
	margin: var(--space-2) 0 0 0;
	font-size: var(--font-size-sm);
	color: var(--text-secondary);
}

/* Badge Component */
.badge {
	display: inline-flex;
	align-items: center;
	padding: var(--space-1) var(--space-3);
	font-size: var(--font-size-xs);
	font-weight: 500;
	border-radius: var(--radius-full);
	text-transform: uppercase;
	letter-spacing: 0.025em;
}

.badge-primary {
	background: rgba(var(--theme2-rgb), 0.1);
	color: var(--theme2);
	border: 1px solid rgba(var(--theme2-rgb), 0.2);
}

.badge-success {
	background: rgba(var(--success-rgb), 0.1);
	color: var(--success);
	border: 1px solid rgba(var(--success-rgb), 0.2);
}

.badge-warning {
	background: rgba(var(--warning-rgb), 0.1);
	color: var(--warning);
	border: 1px solid rgba(var(--warning-rgb), 0.2);
}

.badge-danger {
	background: rgba(var(--danger-rgb), 0.1);
	color: var(--danger);
	border: 1px solid rgba(var(--danger-rgb), 0.2);
}

/* Loading Spinner */
.spinner {
	width: 1.5rem;
	height: 1.5rem;
	border: 2px solid var(--border-color);
	border-top: 2px solid var(--theme2);
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% {
		transform: rotate(0deg);
	}
	100% {
		transform: rotate(360deg);
	}
}

/* Utility Classes */
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}

.text-center {
	text-align: center;
}
.text-left {
	text-align: left;
}
.text-right {
	text-align: right;
}

.font-bold {
	font-weight: 700;
}
.font-semibold {
	font-weight: 600;
}
.font-medium {
	font-weight: 500;
}
.font-normal {
	font-weight: 400;
}

.text-xs {
	font-size: var(--font-size-xs);
}
.text-sm {
	font-size: var(--font-size-sm);
}
.text-base {
	font-size: var(--font-size-base);
}
.text-lg {
	font-size: var(--font-size-lg);
}
.text-xl {
	font-size: var(--font-size-xl);
}
.text-2xl {
	font-size: var(--font-size-2xl);
}
.text-3xl {
	font-size: var(--font-size-3xl);
}

.text-primary {
	color: var(--theme2);
}
.text-secondary {
	color: var(--text-secondary);
}
.text-muted {
	color: var(--text-muted);
}
.text-success {
	color: var(--success);
}
.text-warning {
	color: var(--warning);
}
.text-danger {
	color: var(--danger);
}

.mb-0 {
	margin-bottom: 0;
}
.mb-2 {
	margin-bottom: var(--space-2);
}
.mb-4 {
	margin-bottom: var(--space-4);
}
.mb-6 {
	margin-bottom: var(--space-6);
}
.mb-8 {
	margin-bottom: var(--space-8);
}

.mt-0 {
	margin-top: 0;
}
.mt-2 {
	margin-top: var(--space-2);
}
.mt-4 {
	margin-top: var(--space-4);
}
.mt-6 {
	margin-top: var(--space-6);
}
.mt-8 {
	margin-top: var(--space-8);
}

.flex {
	display: flex;
}
.flex-col {
	flex-direction: column;
}
.flex-row {
	flex-direction: row;
}
.items-center {
	align-items: center;
}
.justify-center {
	justify-content: center;
}
.justify-between {
	justify-content: space-between;
}
.gap-2 {
	gap: var(--space-2);
}
.gap-4 {
	gap: var(--space-4);
}
.gap-6 {
	gap: var(--space-6);
}

.w-full {
	width: 100%;
}
.h-full {
	height: 100%;
}
.min-h-screen {
	min-height: 100vh;
}

.rounded {
	border-radius: var(--radius);
}
.rounded-md {
	border-radius: var(--radius-md);
}
.rounded-lg {
	border-radius: var(--radius-lg);
}
.rounded-xl {
	border-radius: var(--radius-xl);
}
.rounded-full {
	border-radius: var(--radius-full);
}

.shadow {
	box-shadow: var(--shadow);
}
.shadow-md {
	box-shadow: var(--shadow-md);
}
.shadow-lg {
	box-shadow: var(--shadow-lg);
}
.shadow-xl {
	box-shadow: var(--shadow-xl);
}

.transition {
	transition: all 0.2s ease;
}
.hover\:scale-105:hover {
	transform: scale(1.05);
}
.hover\:shadow-lg:hover {
	box-shadow: var(--shadow-lg);
}
