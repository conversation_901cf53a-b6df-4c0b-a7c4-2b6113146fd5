:root {
	--bg: #0e0e10;
	--bg-bright: #18181b;
	--bg-brighter: #3d4146;
	--bg-dark: #121416;
	--theme: #00cc66;
	--theme-bright: #00ff80;
	--theme-rgb: 0, 204, 102;
	--theme2: #2980b9;
	--theme2-bright: #3498db;
	--theme2-rgb: 41, 128, 185;
	--text: #f5f5f5;
	--text-dark: #616161;
	--danger: #e74c3c;
	--danger-rgb: 231, 76, 60;
	--success: #27ae60;
	--success-rgb: 39, 174, 96;
	--warning: #f39c12;
	--warning-rgb: 243, 156, 18;
	--info: #3498db;
	--info-rgb: 52, 152, 219;
	--border-color: #333;
}

body {
	margin: 0;
	padding: 0;
	background: var(--bg);
	color: var(--text);
	font-family:
		-apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif,
		'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
	height: 100%;
	width: 100%;
	line-height: 1.6;
	font-size: 16px;
}

* {
	box-sizing: border-box;
}

html {
	scroll-behavior: smooth;
}

a {
	color: var(--theme2);
	text-decoration: none;
	transition: color 0.2s ease;
}

a:hover {
	color: var(--theme2-bright);
}

button,
input,
select,
textarea {
	font-family: inherit;
	font-size: 1rem;
	padding: 0.5rem 1rem;
	border: 1px solid var(--border-color);
	background-color: var(--bg-dark);
	color: var(--text);
	border-radius: 4px;
	transition: all 0.2s ease;
}

button {
	cursor: pointer;
}

button:hover {
	background-color: var(--bg-brighter);
}

input:focus,
select:focus,
textarea:focus {
	outline: none;
	border-color: var(--theme2);
	box-shadow: 0 0 0 2px rgba(var(--theme2-rgb), 0.2);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: var(--bg-dark);
}

::-webkit-scrollbar-thumb {
	background: var(--border-color);
	border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
	background: var(--text-dark);
}

/* Selection Styling */
::selection {
	background: rgba(var(--theme2-rgb), 0.3);
	color: var(--text);
}

/* Focus Visible for Accessibility */
:focus-visible {
	outline: 2px solid var(--theme2);
	outline-offset: 2px;
}

/* Utility Classes */
.sr-only {
	position: absolute;
	width: 1px;
	height: 1px;
	padding: 0;
	margin: -1px;
	overflow: hidden;
	clip: rect(0, 0, 0, 0);
	white-space: nowrap;
	border: 0;
}
