<script lang="ts">
	import { page } from '$app/stores';
	import { goto } from '$app/navigation';
	import { useChannelsQuery } from '$lib/api';
	import Settings from './Settings.svelte';
	import { showOptout } from '$lib/store';

	let channel = $state($page.url.searchParams.get('channel') || '');
	let username = $state($page.url.searchParams.get('username') || '');

	const channelsQuery = useChannelsQuery();

	function handleSubmit(event: SubmitEvent) {
		event.preventDefault(); // Correct way to prevent default form submission
		const url = new URL($page.url);
		if (channel) {
			url.searchParams.set('channel', channel.toLowerCase().trim());
		} else {
			url.searchParams.delete('channel');
		}
		if (username) {
			url.searchParams.set('username', username.toLowerCase().trim());
		} else {
			url.searchParams.delete('username');
		}
		url.pathname = '/'; // Go to home page on new search
		goto(url, { invalidateAll: true });
	}
</script>

<div class="filters-wrapper">
	<form onsubmit={handleSubmit}>
		<div class="input-group">
			<input
				list="channels-list"
				name="channel"
				placeholder="Channel or ID"
				bind:value={channel}
			/>
			{#if channelsQuery.data}
				<datalist id="channels-list">
					{#each channelsQuery.data as c (c.userID)}
						<option value={c.name}>{c.name}</option>
					{/each}
				</datalist>
			{/if}

			<input name="username" placeholder="Username or ID" bind:value={username} />
			<button type="submit">Load</button>
		</div>

		<div class="nav-group">
			<a href="/" class:active={$page.url.pathname === '/'} title="Home Dashboard">
				🏠 Home
			</a>
			<a href="/search" class:active={$page.url.pathname.startsWith('/search')} title="Search across all channels">
				🔍 Global Search
			</a>
			<a href="/analytics" class:active={$page.url.pathname.startsWith('/analytics')} title="Analytics and insights">
				📊 Analytics
			</a>
			<a href="/tracker" class:active={$page.url.pathname.startsWith('/tracker')} title="Track specific users">
				👥 User Tracker
			</a>
		</div>

		<div class="actions-group">
			<Settings />
			<a href="/redoc" target="_blank" title="API Docs">API</a>
			<button
				type="button"
				onclick={() => showOptout.update((v) => !v)}
				title="Opt-out"
				class:active={$showOptout}>Opt-out</button
			>
		</div>
	</form>
</div>

<style>
	.filters-wrapper {
		padding: var(--space-6) var(--space-8);
		background: linear-gradient(135deg, var(--bg-card) 0%, var(--bg-bright) 100%);
		border-bottom: 1px solid var(--border-color);
		box-shadow: var(--shadow-md);
		backdrop-filter: blur(10px);
		position: sticky;
		top: 0;
		z-index: 100;
	}

	form {
		display: flex;
		flex-wrap: wrap;
		justify-content: space-between;
		align-items: center;
		gap: var(--space-6);
		max-width: 1400px;
		margin: 0 auto;
	}

	.input-group,
	.nav-group,
	.actions-group {
		display: flex;
		flex-wrap: wrap;
		gap: var(--space-3);
		align-items: center;
	}

	.input-group {
		flex: 1;
		min-width: 320px;
	}

	.input-group input {
		padding: var(--space-3) var(--space-4);
		border: 2px solid var(--border-color);
		border-radius: var(--radius-lg);
		background: var(--bg-dark);
		color: var(--text);
		font-size: var(--font-size-sm);
		transition: all 0.3s ease;
		min-width: 140px;
		font-weight: 400;
	}

	.input-group input:focus {
		outline: none;
		border-color: var(--theme2);
		box-shadow: 0 0 0 3px rgba(var(--theme2-rgb), 0.1);
		background: var(--bg-bright);
	}

	.input-group input::placeholder {
		color: var(--text-muted);
	}

	button,
	a {
		padding: var(--space-3) var(--space-4);
		border-radius: var(--radius-lg);
		border: 2px solid transparent;
		transition: all 0.3s ease;
		text-decoration: none;
		font-weight: 500;
		font-size: var(--font-size-sm);
		cursor: pointer;
		display: inline-flex;
		align-items: center;
		gap: var(--space-2);
		white-space: nowrap;
	}

	button[type='submit'] {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		border-color: var(--theme2);
		color: white;
		font-weight: 600;
		box-shadow: var(--shadow-md);
	}

	button[type='submit']:hover {
		transform: translateY(-2px);
		box-shadow: var(--shadow-lg);
		background: linear-gradient(135deg, var(--theme2-dark) 0%, var(--theme2) 100%);
	}

	.nav-group {
		flex: 2;
		justify-content: center;
		gap: var(--space-2);
	}

	.nav-group a {
		background: var(--bg-dark);
		border-color: var(--border-color);
		color: var(--text-secondary);
		position: relative;
		overflow: hidden;
		transition: all 0.3s ease;
	}

	.nav-group a::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(var(--theme2-rgb), 0.1), transparent);
		transition: left 0.6s ease;
	}

	.nav-group a:hover::before {
		left: 100%;
	}

	.nav-group a:hover {
		border-color: var(--theme2);
		color: var(--text);
		transform: translateY(-2px);
		box-shadow: var(--shadow-md);
		background: var(--bg-hover);
	}

	.nav-group a.active {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		border-color: var(--theme2);
		color: white;
		box-shadow: var(--shadow-md);
	}

	.actions-group a,
	.actions-group button {
		background: var(--bg-dark);
		border-color: var(--border-color);
		color: var(--text-secondary);
		transition: all 0.3s ease;
	}

	.actions-group a:hover,
	.actions-group button:hover {
		border-color: var(--theme);
		background: var(--bg-hover);
		color: var(--text);
		transform: translateY(-2px);
		box-shadow: var(--shadow-sm);
	}

	.actions-group button.active {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		border-color: var(--theme);
		color: white;
		box-shadow: var(--shadow-md);
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.filters-wrapper {
			padding: var(--space-4);
		}

		form {
			flex-direction: column;
			gap: var(--space-4);
		}

		.input-group,
		.nav-group,
		.actions-group {
			width: 100%;
			justify-content: center;
		}

		.input-group {
			min-width: auto;
		}

		.nav-group {
			order: -1;
		}

		.nav-group a {
			flex: 1;
			text-align: center;
			min-width: 0;
		}
	}

	@media (max-width: 480px) {
		.filters-wrapper {
			padding: var(--space-3);
		}

		.nav-group {
			flex-direction: column;
			width: 100%;
			gap: var(--space-2);
		}

		.nav-group a,
		.actions-group a,
		.actions-group button {
			width: 100%;
			justify-content: center;
		}
	}
</style>