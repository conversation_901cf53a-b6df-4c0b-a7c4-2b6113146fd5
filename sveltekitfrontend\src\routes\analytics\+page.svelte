<script lang="ts">
	import { useGlobalAnalyticsQuery, usePeakActivityQuery, useEngagementMetricsQuery } from '$lib/api';
	import { subDays, startOfDay, format } from 'date-fns';
	import Bar<PERSON>hart from '$lib/components/charts/BarChart.svelte';
	import DoughnutChart from '$lib/components/charts/DoughnutChart.svelte';
	import Line<PERSON>hart from '$lib/components/charts/LineChart.svelte';
	import Heatmap<PERSON><PERSON> from '$lib/components/charts/HeatmapChart.svelte';

	let dateRange = $state(30);
	let selectedTab = $state<'overview' | 'activity' | 'engagement'>('overview');

	const to = $derived(startOfDay(new Date()));
	const from = $derived(subDays(to, dateRange));

	const analyticsQuery = $derived(useGlobalAnalyticsQuery(from, to));
	const peakActivityQuery = $derived(usePeakActivityQuery(from, to));
	const engagementQuery = $derived(useEngagementMetricsQuery(from, to));

	// Transform data for charts
	const topUsersChartData = $derived(() => {
		if (!$analyticsQuery.data?.topUsers) return [];
		return $analyticsQuery.data.topUsers.slice(0, 10).map(user => ({
			x: user.username,
			y: user.messageCount,
			label: user.username
		}));
	});

	const topChannelsChartData = $derived(() => {
		if (!$analyticsQuery.data?.topChannels) return [];
		return $analyticsQuery.data.topChannels.slice(0, 10).map(channel => ({
			x: channel.channelName,
			y: channel.messageCount,
			label: channel.channelName
		}));
	});

	const activityTimelineData = $derived(() => {
		if (!$peakActivityQuery.data?.dailyPeaks) return [];
		return $peakActivityQuery.data.dailyPeaks.map(peak => ({
			x: peak.date,
			y: peak.peakMessageCount,
			label: format(new Date(peak.date), 'MMM dd')
		}));
	});
</script>

<div class="container">
	<div class="dashboard-header">
		<div class="header-content">
			<div class="title-section">
				<h1>📊 Analytics Dashboard</h1>
				<p>Comprehensive insights into chat activity and user engagement</p>
			</div>
			<div class="controls">
				<select bind:value={dateRange} class="date-range-select">
					<option value={7}>Last 7 days</option>
					<option value={30}>Last 30 days</option>
					<option value={90}>Last 90 days</option>
					<option value={365}>Last year</option>
				</select>
			</div>
		</div>
	</div>

	<!-- Tab Navigation -->
	<div class="tab-navigation">
		<button
			class="tab"
			class:active={selectedTab === 'overview'}
			onclick={() => (selectedTab = 'overview')}
		>
			📈 Overview
		</button>
		<button
			class="tab"
			class:active={selectedTab === 'activity'}
			onclick={() => (selectedTab = 'activity')}
		>
			⚡ Activity Patterns
		</button>
		<button
			class="tab"
			class:active={selectedTab === 'engagement'}
			onclick={() => (selectedTab = 'engagement')}
		>
			👥 User Engagement
		</button>
	</div>

	{#if $analyticsQuery.isPending}
		<div class="loading">
			<div class="spinner"></div>
			<p>Loading analytics data...</p>
		</div>
	{:else if $analyticsQuery.isError}
		<div class="error-container">
			<p class="error">Error loading analytics: {$analyticsQuery.error.message}</p>
		</div>
	{:else if $analyticsQuery.data}
		{@const data = $analyticsQuery.data}

		<!-- Overview Tab -->
		{#if selectedTab === 'overview'}
			<!-- Key Metrics -->
			<div class="metrics-grid">
				<div class="metric-card primary">
					<div class="metric-icon">💬</div>
					<div class="metric-content">
						<h3>Total Messages</h3>
						<p class="metric-value">{data.totalMessages.toLocaleString()}</p>
						<p class="metric-subtitle">
							{(data.totalMessages / dateRange).toFixed(0)} per day average
						</p>
					</div>
				</div>
				<div class="metric-card secondary">
					<div class="metric-icon">👤</div>
					<div class="metric-content">
						<h3>Active Users</h3>
						<p class="metric-value">{data.totalUsers.toLocaleString()}</p>
						<p class="metric-subtitle">
							{data.growthMetrics.newUsersPerDay.toFixed(1)} new per day
						</p>
					</div>
				</div>
				<div class="metric-card accent">
					<div class="metric-icon">📺</div>
					<div class="metric-content">
						<h3>Active Channels</h3>
						<p class="metric-value">{data.totalChannels.toLocaleString()}</p>
						<p class="metric-subtitle">
							{(data.totalMessages / data.totalChannels).toFixed(0)} messages per channel
						</p>
					</div>
				</div>
				<div class="metric-card success">
					<div class="metric-icon">📊</div>
					<div class="metric-content">
						<h3>Engagement Rate</h3>
						<p class="metric-value">{(data.totalMessages / data.totalUsers).toFixed(1)}</p>
						<p class="metric-subtitle">messages per user</p>
					</div>
				</div>
			</div>

			<!-- Charts Grid -->
			<div class="charts-grid">
				<div class="chart-card full-width">
					{#if data.activityHeatmap?.length > 0}
						<HeatmapChart
							data={data.activityHeatmap}
							title="24-Hour Activity Heatmap"
							height={200}
						/>
					{/if}
				</div>

				<div class="chart-card">
					{#if data.messageDistribution?.length > 0}
						<DoughnutChart data={data.messageDistribution} title="Message Type Distribution" />
					{/if}
				</div>

				<div class="chart-card">
					{#if topChannelsChartData.length > 0}
						<BarChart
							data={topChannelsChartData}
							title="Top Channels by Activity"
							xLabel="Channel"
							yLabel="Messages"
						/>
					{/if}
				</div>
			</div>
		{/if}

		<!-- Activity Patterns Tab -->
		{#if selectedTab === 'activity'}
			<div class="charts-grid">
				{#if $peakActivityQuery.data}
					<div class="chart-card full-width">
						{#if activityTimelineData.length > 0}
							<LineChart
								data={activityTimelineData}
								title="Daily Peak Activity Timeline"
								xLabel="Date"
								yLabel="Peak Messages"
								color="var(--theme)"
							/>
						{/if}
					</div>

					<div class="chart-card">
						{#if $peakActivityQuery.data.weeklyPatterns?.length > 0}
							<BarChart
								data={$peakActivityQuery.data.weeklyPatterns.map(pattern => ({
									x: ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'][pattern.dayOfWeek],
									y: pattern.avgMessageCount,
									label: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'][pattern.dayOfWeek]
								}))}
								title="Weekly Activity Patterns"
								xLabel="Day of Week"
								yLabel="Average Messages"
							/>
						{/if}
					</div>

					<div class="chart-card">
						{#if $peakActivityQuery.data.activityTrends?.length > 0}
							<LineChart
								data={$peakActivityQuery.data.activityTrends.map(trend => ({
									x: trend.period,
									y: trend.messageGrowth,
									label: trend.period
								}))}
								title="Message Growth Trends"
								xLabel="Period"
								yLabel="Growth %"
								color="var(--theme2)"
							/>
						{/if}
					</div>
				{:else if $peakActivityQuery.isPending}
					<div class="loading">
						<div class="spinner"></div>
						<p>Loading activity patterns...</p>
					</div>
				{:else if $peakActivityQuery.isError}
					<div class="error-container">
						<p class="error">Error loading activity data: {$peakActivityQuery.error.message}</p>
					</div>
				{/if}
			</div>
		{/if}

		<!-- User Engagement Tab -->
		{#if selectedTab === 'engagement'}
			<div class="charts-grid">
				{#if $engagementQuery.data}
					<div class="chart-card">
						{#if topUsersChartData.length > 0}
							<BarChart
								data={topUsersChartData}
								title="Top Users by Activity"
								xLabel="User"
								yLabel="Messages"
							/>
						{/if}
					</div>

					<div class="chart-card">
						{#if $engagementQuery.data.userEngagement?.length > 0}
							<LineChart
								data={$engagementQuery.data.userEngagement.slice(0, 10).map(user => ({
									x: user.username,
									y: user.engagementScore,
									label: user.username
								}))}
								title="User Engagement Scores"
								xLabel="User"
								yLabel="Engagement Score"
								color="var(--theme)"
							/>
						{/if}
					</div>

					<div class="chart-card full-width">
						{#if $engagementQuery.data.channelGrowth?.length > 0}
							<BarChart
								data={$engagementQuery.data.channelGrowth.slice(0, 15).map(channel => ({
									x: channel.channelName,
									y: channel.growthRate,
									label: channel.channelName
								}))}
								title="Channel Growth Rates"
								xLabel="Channel"
								yLabel="Growth Rate %"
							/>
						{/if}
					</div>
				{:else if $engagementQuery.isPending}
					<div class="loading">
						<div class="spinner"></div>
						<p>Loading engagement metrics...</p>
					</div>
				{:else if $engagementQuery.isError}
					<div class="error-container">
						<p class="error">Error loading engagement data: {$engagementQuery.error.message}</p>
					</div>
				{/if}
			</div>
		{/if}
	{/if}
</div>

<style>
	.container {
		padding: var(--space-6);
		max-width: 1400px;
		margin: 0 auto;
		min-height: 100vh;
	}

	.dashboard-header {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		color: white;
		padding: var(--space-12) var(--space-8);
		border-radius: var(--radius-2xl);
		margin-bottom: var(--space-8);
		box-shadow: var(--shadow-xl);
		position: relative;
		overflow: hidden;
	}

	.dashboard-header::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
		pointer-events: none;
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: var(--space-4);
		position: relative;
		z-index: 1;
	}

	.title-section h1 {
		margin: 0 0 var(--space-2) 0;
		font-size: var(--font-size-4xl);
		font-weight: 700;
	}

	.title-section p {
		margin: 0;
		opacity: 0.95;
		font-size: var(--font-size-lg);
		font-weight: 400;
	}

	.controls {
		display: flex;
		gap: var(--space-4);
		align-items: center;
	}

	.date-range-select {
		padding: var(--space-3) var(--space-4);
		border-radius: var(--radius-lg);
		border: 2px solid rgba(255, 255, 255, 0.2);
		background: rgba(255, 255, 255, 0.1);
		color: white;
		font-size: var(--font-size-sm);
		font-weight: 500;
		backdrop-filter: blur(10px);
		transition: all 0.3s ease;
	}

	.date-range-select:hover {
		border-color: rgba(255, 255, 255, 0.4);
		background: rgba(255, 255, 255, 0.15);
	}

	.date-range-select:focus {
		outline: none;
		border-color: rgba(255, 255, 255, 0.6);
		box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
	}

	.date-range-select option {
		background: var(--bg-dark);
		color: var(--text);
	}

	.tab-navigation {
		display: flex;
		gap: var(--space-2);
		margin-bottom: var(--space-8);
		background: var(--bg-card);
		padding: var(--space-2);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
	}

	.tab {
		flex: 1;
		padding: var(--space-3) var(--space-4);
		border: none;
		background: transparent;
		color: var(--text-secondary);
		border-radius: var(--radius-lg);
		cursor: pointer;
		transition: all 0.3s ease;
		font-size: var(--font-size-sm);
		font-weight: 500;
		position: relative;
		text-align: center;
	}

	.tab:hover {
		background: var(--bg-hover);
		color: var(--text);
		transform: translateY(-1px);
		box-shadow: var(--shadow-sm);
	}

	.tab.active {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		box-shadow: var(--shadow-md);
		transform: translateY(-2px);
	}

	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: var(--space-16);
		gap: var(--space-4);
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
	}

	.loading p {
		color: var(--text-secondary);
		font-size: var(--font-size-lg);
		font-weight: 500;
		margin: 0;
	}

	.spinner {
		width: 3rem;
		height: 3rem;
		border: 3px solid var(--border-color);
		border-top: 3px solid var(--theme);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.error-container {
		padding: var(--space-8);
		text-align: center;
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
	}

	.error {
		color: var(--danger);
		background: rgba(var(--danger-rgb), 0.1);
		padding: var(--space-4);
		border-radius: var(--radius-lg);
		border: 1px solid rgba(var(--danger-rgb), 0.2);
		border-left: 4px solid var(--danger);
		font-weight: 500;
	}

	.metrics-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
		gap: var(--space-6);
		margin-bottom: var(--space-8);
	}

	.metric-card {
		display: flex;
		align-items: center;
		gap: var(--space-4);
		padding: var(--space-6);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		transition: all 0.3s ease;
		position: relative;
		overflow: hidden;
		box-shadow: var(--shadow);
	}

	.metric-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 4px;
		height: 100%;
		background: linear-gradient(to bottom, var(--theme2), var(--theme2-bright));
		transition: width 0.3s ease;
	}

	.metric-card:hover {
		transform: translateY(-4px);
		box-shadow: var(--shadow-lg);
		border-color: var(--border-light);
	}

	.metric-card:hover::before {
		width: 6px;
	}

	.metric-card.primary {
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		box-shadow: var(--shadow-md);
	}

	.metric-card.secondary {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		color: white;
		box-shadow: var(--shadow-md);
	}

	.metric-card.accent {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		color: white;
		box-shadow: var(--shadow-md);
	}

	.metric-card.success {
		background: linear-gradient(135deg, var(--success) 0%, #2ecc71 100%);
		color: white;
		box-shadow: var(--shadow-md);
	}

	.metric-icon {
		font-size: 3rem;
		opacity: 0.9;
		filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
		transition: transform 0.3s ease;
	}

	.metric-card:hover .metric-icon {
		transform: scale(1.1);
	}

	.metric-content {
		flex: 1;
		z-index: 1;
		position: relative;
	}

	.metric-content h3 {
		margin: 0 0 var(--space-2) 0;
		font-size: var(--font-size-base);
		font-weight: 600;
		opacity: 0.95;
		text-transform: uppercase;
		letter-spacing: 0.025em;
	}

	.metric-value {
		margin: 0 0 var(--space-1) 0;
		font-size: var(--font-size-3xl);
		font-weight: 700;
		line-height: var(--leading-tight);
	}

	.metric-subtitle {
		margin: 0;
		font-size: var(--font-size-sm);
		opacity: 0.85;
		font-weight: 400;
	}

	.charts-grid {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(450px, 1fr));
		gap: var(--space-6);
	}

	.chart-card {
		background: var(--bg-card);
		border-radius: var(--radius-xl);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
		transition: all 0.3s ease;
	}

	.chart-card:hover {
		border-color: var(--border-light);
		box-shadow: var(--shadow-md);
		transform: translateY(-2px);
	}

	.chart-card.full-width {
		grid-column: 1 / -1;
	}

	/* Responsive Design */
	@media (max-width: 768px) {
		.container {
			padding: var(--space-4);
		}

		.dashboard-header {
			padding: var(--space-8) var(--space-4);
		}

		.header-content {
			flex-direction: column;
			text-align: center;
		}

		.title-section h1 {
			font-size: var(--font-size-3xl);
		}

		.tab-navigation {
			flex-direction: column;
		}

		.metrics-grid {
			grid-template-columns: 1fr;
		}

		.charts-grid {
			grid-template-columns: 1fr;
		}

		.metric-card {
			padding: var(--space-4);
		}
	}
</style>