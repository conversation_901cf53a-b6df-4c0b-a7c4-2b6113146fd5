<script lang="ts">
	import type { TrackedUser, FullLogMessage } from '$lib/types';
	import { useAdvancedSearchQuery, useUserAnalyticsQuery } from '$lib/api';
	import LogLine from '$lib/components/LogLine.svelte';
	import SearchResults from '$lib/components/SearchResults.svelte';
	import BarChart from '$lib/components/charts/BarChart.svelte';
	import LineChart from '$lib/components/charts/LineChart.svelte';
	import { subDays, format } from 'date-fns';

	let trackedUsers = $state<TrackedUser[]>([]);
	let newUser = $state('');
	let selectedUser = $state<TrackedUser | null>(null);
	let timeRange = $state(7); // days
	let selectedTab = $state<'overview' | 'activity' | 'messages'>('overview');

	const to = $derived(new Date());
	const from = $derived(subDays(to, timeRange));

	// Get analytics for all tracked users
	const userAnalyticsQuery = $derived(
		useUserAnalyticsQuery(trackedUsers.map(u => u.username), from, to)
	);

	// Get recent messages for tracked users
	const userSearchParams = $derived({
		users: trackedUsers.map((u) => u.username),
		from: from.toISOString(),
		to: to.toISOString()
	});
	const userMessagesQuery = $derived(useAdvancedSearchQuery(userSearchParams, 'global', undefined));

	// Transform data for selected user
	const selectedUserData = $derived(() => {
		if (!selectedUser || !$userAnalyticsQuery.data) return null;

		const userData = $userAnalyticsQuery.data.topUsers?.find(
			u => u.username === selectedUser.username
		);

		const userMessages = $userMessagesQuery.data?.filter(
			msg => msg.username === selectedUser.username || msg.displayName === selectedUser.username
		) || [];

		// Group messages by channel
		const messagesByChannel = userMessages.reduce((acc, msg) => {
			if (!acc[msg.channel]) {
				acc[msg.channel] = [];
			}
			acc[msg.channel].push(msg);
			return acc;
		}, {} as Record<string, FullLogMessage[]>);

		// Calculate channel stats
		const channelStats = Object.entries(messagesByChannel).map(([channel, messages]) => ({
			channel,
			messageCount: messages.length,
			percentage: (messages.length / userMessages.length) * 100,
			latestMessage: messages.sort((a, b) =>
				new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
			)[0]
		})).sort((a, b) => b.messageCount - a.messageCount);

		return {
			userData,
			userMessages,
			messagesByChannel,
			channelStats,
			totalMessages: userMessages.length,
			totalChannels: channelStats.length
		};
	});

	function addUser(event: SubmitEvent) {
		event.preventDefault();
		if (newUser && !trackedUsers.find((u) => u.username === newUser)) {
			trackedUsers = [
				...trackedUsers,
				{
					userId: newUser,
					username: newUser,
					addedAt: new Date()
				}
			];
			newUser = '';
		}
	}

	function removeUser(username: string) {
		trackedUsers = trackedUsers.filter((u) => u.username !== username);
		if (selectedUser?.username === username) {
			selectedUser = null;
		}
	}

	function selectUser(user: TrackedUser) {
		selectedUser = selectedUser?.username === user.username ? null : user;
	}

	// Get user activity summary
	function getUserSummary(username: string) {
		if (!$userAnalyticsQuery.data) return null;

		const userData = $userAnalyticsQuery.data.topUsers?.find(u => u.username === username);
		const userMessages = $userMessagesQuery.data?.filter(
			msg => msg.username === username || msg.displayName === username
		) || [];

		return {
			messageCount: userData?.messageCount || 0,
			channelsActive: userData?.channelsActive || 0,
			recentMessages: userMessages.length,
			avgMessageLength: userData?.avgMessageLength || 0
		};
	}
</script>

<div class="container">
	<div class="tracker-header">
		<div class="header-content">
			<div class="title-section">
				<h1>👥 User Tracker</h1>
				<p>Monitor specific users across all channels with detailed activity insights</p>
			</div>
			<div class="controls">
				<select bind:value={timeRange} class="time-range-select">
					<option value={1}>Last 24 hours</option>
					<option value={7}>Last 7 days</option>
					<option value={30}>Last 30 days</option>
					<option value={90}>Last 90 days</option>
				</select>
			</div>
		</div>
	</div>

	<!-- Add User Form -->
	<div class="add-user-section">
		<form class="add-form" onsubmit={addUser}>
			<input
				type="search"
				placeholder="Enter username to track..."
				bind:value={newUser}
				class="user-input"
			/>
			<button type="submit" class="add-button">📌 Track User</button>
		</form>
	</div>

	<!-- Tracked Users Grid -->
	{#if trackedUsers.length > 0}
		<div class="tracked-users-section">
			<h3>Tracked Users ({trackedUsers.length})</h3>
			<div class="users-grid">
				{#each trackedUsers as user (user.username)}
					{@const summary = getUserSummary(user.username)}
					<div
						class="user-card"
						class:selected={selectedUser?.username === user.username}
						onclick={() => selectUser(user)}
						onkeydown={(e) => e.key === 'Enter' && selectUser(user)}
						role="button"
						tabindex="0"
					>
						<div class="user-header">
							<div class="user-info">
								<h4>{user.username}</h4>
								<p class="added-date">Added {user.addedAt.toLocaleDateString()}</p>
							</div>
							<button
								class="remove-button"
								onclick={(e) => {
									e.stopPropagation();
									removeUser(user.username);
								}}
								title="Remove user"
							>
								×
							</button>
						</div>

						{#if summary}
							<div class="user-stats">
								<div class="stat">
									<span class="stat-value">{summary.messageCount}</span>
									<span class="stat-label">Messages</span>
								</div>
								<div class="stat">
									<span class="stat-value">{summary.channelsActive}</span>
									<span class="stat-label">Channels</span>
								</div>
								<div class="stat">
									<span class="stat-value">{summary.recentMessages}</span>
									<span class="stat-label">Recent</span>
								</div>
							</div>
						{/if}
					</div>
				{/each}
			</div>
		</div>

		<!-- Loading State -->
		{#if $userAnalyticsQuery.isPending}
			<div class="loading">
				<div class="spinner"></div>
				<p>Loading user analytics...</p>
			</div>
		{:else if $userAnalyticsQuery.isError}
			<div class="error-container">
				<p class="error">Error loading analytics: {$userAnalyticsQuery.error.message}</p>
			</div>
		{/if}

		<!-- Selected User Details -->
		{#if selectedUser && selectedUserData()}
			{@const userData = selectedUserData()}
			<div class="user-details">
				<div class="details-header">
					<h3>📊 {selectedUser.username} - Activity Details</h3>
					<div class="tab-navigation">
						<button
							class="tab"
							class:active={selectedTab === 'overview'}
							onclick={() => (selectedTab = 'overview')}
						>
							Overview
						</button>
						<button
							class="tab"
							class:active={selectedTab === 'activity'}
							onclick={() => (selectedTab = 'activity')}
						>
							Channel Activity
						</button>
						<button
							class="tab"
							class:active={selectedTab === 'messages'}
							onclick={() => (selectedTab = 'messages')}
						>
							Recent Messages
						</button>
					</div>
				</div>

				<!-- Overview Tab -->
				{#if selectedTab === 'overview'}
					<div class="overview-content">
						<div class="metrics-row">
							<div class="metric-card">
								<div class="metric-icon">💬</div>
								<div class="metric-content">
									<span class="metric-value">{userData?.totalMessages || 0}</span>
									<span class="metric-label">Total Messages</span>
								</div>
							</div>
							<div class="metric-card">
								<div class="metric-icon">📺</div>
								<div class="metric-content">
									<span class="metric-value">{userData?.totalChannels || 0}</span>
									<span class="metric-label">Active Channels</span>
								</div>
							</div>
							<div class="metric-card">
								<div class="metric-icon">📊</div>
								<div class="metric-content">
									<span class="metric-value">
										{userData?.userData?.avgMessageLength?.toFixed(1) || '0'}
									</span>
									<span class="metric-label">Avg Message Length</span>
								</div>
							</div>
							<div class="metric-card">
								<div class="metric-icon">⚡</div>
								<div class="metric-content">
									<span class="metric-value">
										{((userData?.totalMessages || 0) / timeRange).toFixed(1)}
									</span>
									<span class="metric-label">Messages/Day</span>
								</div>
							</div>
						</div>

						{#if userData?.channelStats && userData.channelStats.length > 0}
							<div class="chart-container">
								<BarChart
									data={userData.channelStats.slice(0, 10).map(stat => ({
										x: stat.channel,
										y: stat.messageCount,
										label: `#${stat.channel}`
									}))}
									title="Messages by Channel"
									xLabel="Channel"
									yLabel="Messages"
									color="var(--theme2)"
								/>
							</div>
						{/if}
					</div>
				{/if}

				<!-- Channel Activity Tab -->
				{#if selectedTab === 'activity'}
					<div class="activity-content">
						<div class="channels-list">
							{#each userData?.channelStats || [] as stat}
								<div class="channel-item">
									<div class="channel-header">
										<h4>#{stat.channel}</h4>
										<div class="channel-stats">
											<span class="message-count">{stat.messageCount} messages</span>
											<span class="percentage">({stat.percentage.toFixed(1)}%)</span>
										</div>
									</div>
									<div class="channel-details">
										<p class="last-activity">
											Last activity: {stat.latestMessage ?
												new Date(stat.latestMessage.timestamp).toLocaleString() :
												'No recent activity'
											}
										</p>
										<div class="progress-bar">
											<div
												class="progress-fill"
												style="width: {stat.percentage}%"
											></div>
										</div>
									</div>
								</div>
							{/each}
						</div>
					</div>
				{/if}

				<!-- Messages Tab -->
				{#if selectedTab === 'messages'}
					<div class="messages-content">
						<SearchResults
							results={userData?.userMessages || []}
							isLoading={$userMessagesQuery.isPending}
							error={$userMessagesQuery.error}
							searchQuery={selectedUser.username}
							isGlobalSearch={true}
						/>
					</div>
				{/if}
			</div>
		{/if}
	{:else}
		<div class="empty-state">
			<div class="empty-icon">👥</div>
			<h3>No Users Tracked</h3>
			<p>Add users above to start tracking their activity across all channels.</p>
		</div>
	{/if}
</div>

<style>
	.container {
		padding: var(--space-6);
		max-width: 1400px;
		margin: 0 auto;
		min-height: 100vh;
	}

	.tracker-header {
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		color: white;
		padding: var(--space-12) var(--space-8);
		border-radius: var(--radius-2xl);
		margin-bottom: var(--space-8);
		box-shadow: var(--shadow-xl);
		position: relative;
		overflow: hidden;
	}

	.tracker-header::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%);
		pointer-events: none;
	}

	.header-content {
		display: flex;
		justify-content: space-between;
		align-items: center;
		flex-wrap: wrap;
		gap: var(--space-4);
		position: relative;
		z-index: 1;
	}

	.title-section h1 {
		margin: 0 0 var(--space-2) 0;
		font-size: var(--font-size-4xl);
		font-weight: 700;
	}

	.title-section p {
		margin: 0;
		opacity: 0.95;
		font-size: var(--font-size-lg);
		font-weight: 400;
	}

	.controls {
		display: flex;
		gap: var(--space-4);
		align-items: center;
	}

	.time-range-select {
		padding: var(--space-3) var(--space-4);
		border-radius: var(--radius-lg);
		border: 2px solid rgba(255, 255, 255, 0.2);
		background: rgba(255, 255, 255, 0.1);
		color: white;
		font-size: var(--font-size-sm);
		font-weight: 500;
		backdrop-filter: blur(10px);
		transition: all 0.3s ease;
	}

	.time-range-select:hover {
		border-color: rgba(255, 255, 255, 0.4);
		background: rgba(255, 255, 255, 0.15);
	}

	.time-range-select:focus {
		outline: none;
		border-color: rgba(255, 255, 255, 0.6);
		box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
	}

	.time-range-select option {
		background: var(--bg-dark);
		color: var(--text);
	}

	.add-user-section {
		background: var(--bg-card);
		padding: var(--space-8);
		border-radius: var(--radius-xl);
		margin-bottom: var(--space-8);
		border: 1px solid var(--border-color);
		box-shadow: var(--shadow);
		transition: all 0.3s ease;
	}

	.add-user-section:hover {
		border-color: var(--border-light);
		box-shadow: var(--shadow-md);
	}

	.add-form {
		display: flex;
		gap: var(--space-4);
		align-items: center;
	}

	.user-input {
		flex: 1;
		padding: var(--space-4);
		font-size: var(--font-size-base);
		border: 2px solid var(--border-color);
		border-radius: var(--radius-lg);
		background: var(--bg-dark);
		color: var(--text);
		transition: all 0.3s ease;
		font-weight: 400;
	}

	.user-input:focus {
		outline: none;
		border-color: var(--theme);
		box-shadow: 0 0 0 3px rgba(var(--theme-rgb), 0.1);
		background: var(--bg-bright);
	}

	.user-input::placeholder {
		color: var(--text-muted);
	}

	.add-button {
		padding: var(--space-4) var(--space-6);
		background: linear-gradient(135deg, var(--theme) 0%, var(--theme-bright) 100%);
		border: 2px solid var(--theme);
		color: white;
		border-radius: var(--radius-lg);
		font-size: var(--font-size-sm);
		font-weight: 600;
		cursor: pointer;
		transition: all 0.3s ease;
		box-shadow: var(--shadow-sm);
		white-space: nowrap;
	}

	.add-button:hover {
		background: linear-gradient(135deg, var(--theme-bright) 0%, var(--theme) 100%);
		border-color: var(--theme-bright);
		transform: translateY(-1px);
		box-shadow: var(--shadow-md);
	}

	.tracked-users-section {
		margin-bottom: var(--space-8);
	}

	.tracked-users-section h3 {
		margin: 0 0 var(--space-6) 0;
		color: var(--text);
		font-size: var(--font-size-xl);
		font-weight: 600;
	}

	.users-grid {
		display: grid;
		grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
		gap: var(--space-6);
	}

	.user-card {
		background: var(--bg-card);
		border: 2px solid var(--border-color);
		border-radius: var(--radius-xl);
		padding: var(--space-6);
		cursor: pointer;
		transition: all 0.3s ease;
		position: relative;
		box-shadow: var(--shadow);
		overflow: hidden;
	}

	.user-card::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		width: 4px;
		height: 100%;
		background: linear-gradient(to bottom, var(--theme2), var(--theme2-bright));
		transition: width 0.3s ease;
	}

	.user-card:hover {
		border-color: var(--theme2);
		transform: translateY(-4px);
		box-shadow: var(--shadow-lg);
	}

	.user-card:hover::before {
		width: 6px;
	}

	.user-card.selected {
		border-color: var(--theme2);
		background: linear-gradient(135deg, var(--theme2) 0%, var(--theme2-bright) 100%);
		color: white;
		box-shadow: var(--shadow-xl);
	}

	.user-card.selected::before {
		width: 100%;
		background: rgba(255, 255, 255, 0.1);
	}

	.user-header {
		display: flex;
		justify-content: space-between;
		align-items: flex-start;
		margin-bottom: 1rem;
	}

	.user-info h4 {
		margin: 0 0 0.25rem 0;
		font-size: 1.1rem;
		font-weight: 600;
	}

	.added-date {
		margin: 0;
		font-size: 0.875rem;
		opacity: 0.7;
	}

	.remove-button {
		background: none;
		border: none;
		color: var(--text-dark);
		cursor: pointer;
		font-size: 1.5rem;
		padding: 0;
		line-height: 1;
		transition: color 0.2s;
	}

	.remove-button:hover {
		color: var(--danger);
	}

	.user-card.selected .remove-button {
		color: rgba(255, 255, 255, 0.7);
	}

	.user-card.selected .remove-button:hover {
		color: white;
	}

	.user-stats {
		display: grid;
		grid-template-columns: repeat(3, 1fr);
		gap: 0.5rem;
	}

	.stat {
		text-align: center;
		padding: 0.5rem;
		background: var(--bg-dark);
		border-radius: 8px;
	}

	.user-card.selected .stat {
		background: rgba(255, 255, 255, 0.1);
	}

	.stat-value {
		display: block;
		font-size: 1.25rem;
		font-weight: 700;
		color: var(--theme2);
	}

	.user-card.selected .stat-value {
		color: white;
	}

	.stat-label {
		display: block;
		font-size: 0.75rem;
		opacity: 0.7;
		margin-top: 0.25rem;
	}

	.loading {
		display: flex;
		flex-direction: column;
		align-items: center;
		padding: 3rem;
		gap: 1rem;
	}

	.spinner {
		width: 3rem;
		height: 3rem;
		border: 4px solid var(--bg-bright);
		border-top: 4px solid var(--theme);
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}

	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.error-container {
		padding: 2rem;
		text-align: center;
	}

	.error {
		color: var(--danger);
		background: var(--bg-bright);
		padding: 1rem;
		border-radius: 8px;
		border-left: 4px solid var(--danger);
	}

	.user-details {
		background: var(--bg-bright);
		border-radius: 12px;
		border: 1px solid var(--border-color);
		overflow: hidden;
	}

	.details-header {
		padding: 1.5rem;
		border-bottom: 1px solid var(--border-color);
		background: var(--bg-dark);
	}

	.details-header h3 {
		margin: 0 0 1rem 0;
		color: var(--theme2);
		font-size: 1.5rem;
	}

	.tab-navigation {
		display: flex;
		gap: 0.5rem;
	}

	.tab {
		padding: 0.5rem 1rem;
		border: none;
		background: transparent;
		color: var(--text-dark);
		border-radius: 6px;
		cursor: pointer;
		transition: all 0.2s;
		font-size: 0.9rem;
	}

	.tab:hover {
		background: var(--bg-bright);
		color: var(--text);
	}

	.tab.active {
		background: var(--theme2);
		color: white;
	}

	.overview-content,
	.activity-content,
	.messages-content {
		padding: 1.5rem;
	}

	.metrics-row {
		display: grid;
		grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
		gap: 1rem;
		margin-bottom: 2rem;
	}

	.metric-card {
		display: flex;
		align-items: center;
		gap: 1rem;
		padding: 1rem;
		background: var(--bg-dark);
		border-radius: 8px;
		border: 1px solid var(--border-color);
	}

	.metric-icon {
		font-size: 2rem;
		opacity: 0.8;
	}

	.metric-content {
		display: flex;
		flex-direction: column;
	}

	.metric-value {
		font-size: 1.5rem;
		font-weight: 700;
		color: var(--theme2);
		line-height: 1;
	}

	.metric-label {
		font-size: 0.875rem;
		color: var(--text-dark);
		margin-top: 0.25rem;
	}

	.chart-container {
		background: var(--bg-dark);
		border-radius: 8px;
		border: 1px solid var(--border-color);
		min-height: 400px;
	}

	.channels-list {
		display: flex;
		flex-direction: column;
		gap: 1rem;
	}

	.channel-item {
		background: var(--bg-dark);
		border-radius: 8px;
		padding: 1rem;
		border: 1px solid var(--border-color);
	}

	.channel-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 0.5rem;
	}

	.channel-header h4 {
		margin: 0;
		color: var(--theme2);
		font-size: 1.1rem;
	}

	.channel-stats {
		display: flex;
		gap: 0.5rem;
		align-items: center;
	}

	.message-count {
		font-weight: 600;
		color: var(--text);
	}

	.percentage {
		color: var(--text-dark);
		font-size: 0.875rem;
	}

	.channel-details {
		margin-top: 0.5rem;
	}

	.last-activity {
		margin: 0 0 0.5rem 0;
		font-size: 0.875rem;
		color: var(--text-dark);
	}

	.progress-bar {
		width: 100%;
		height: 6px;
		background: var(--bg-bright);
		border-radius: 3px;
		overflow: hidden;
	}

	.progress-fill {
		height: 100%;
		background: var(--theme2);
		transition: width 0.3s ease;
	}

	.empty-state {
		text-align: center;
		padding: 4rem 2rem;
		color: var(--text-dark);
	}

	.empty-icon {
		font-size: 4rem;
		margin-bottom: 1rem;
		opacity: 0.5;
	}

	.empty-state h3 {
		margin: 0 0 0.5rem 0;
		color: var(--text);
	}

	.empty-state p {
		margin: 0;
		font-size: 1.1rem;
	}
</style>