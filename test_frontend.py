#!/usr/bin/env python3
"""
Frontend Testing Script for RustLog SvelteKit Frontend

This script tests the key functionality of the improved SvelteKit frontend
by making HTTP requests to verify that all pages load correctly and
the API integration is working.

Requirements:
- Python 3.6+
- requests library (pip install requests)
- RustLog backend running on localhost:8025
- SvelteKit frontend running on localhost:5173

Usage:
    python test_frontend.py
"""

import requests
import json
import time
import sys
from typing import Dict, List, Optional

# Configuration
FRONTEND_URL = "http://localhost:5173"
BACKEND_URL = "http://localhost:8025"
TIMEOUT = 10

class FrontendTester:
    def __init__(self):
        self.session = requests.Session()
        self.session.timeout = TIMEOUT
        self.results = []
        
    def log_result(self, test_name: str, success: bool, message: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.results.append({
            "test": test_name,
            "success": success,
            "message": message
        })
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")
    
    def test_backend_connection(self) -> bool:
        """Test if backend is accessible"""
        try:
            response = self.session.get(f"{BACKEND_URL}/api/health", timeout=5)
            if response.status_code == 200:
                self.log_result("Backend Connection", True, "Backend is accessible")
                return True
            else:
                self.log_result("Backend Connection", False, f"Backend returned status {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            self.log_result("Backend Connection", False, f"Cannot connect to backend: {e}")
            return False
    
    def test_frontend_pages(self) -> bool:
        """Test if all frontend pages load correctly"""
        pages = [
            ("/", "Dashboard Home"),
            ("/search", "Global Search"),
            ("/analytics", "Analytics Dashboard"),
            ("/tracker", "User Tracker")
        ]
        
        all_passed = True
        for path, name in pages:
            try:
                response = self.session.get(f"{FRONTEND_URL}{path}")
                if response.status_code == 200:
                    # Check if it's actually HTML content
                    if "<!DOCTYPE html>" in response.text or "<html" in response.text:
                        self.log_result(f"Page Load: {name}", True, f"Page loads correctly at {path}")
                    else:
                        self.log_result(f"Page Load: {name}", False, f"Page at {path} doesn't return HTML")
                        all_passed = False
                else:
                    self.log_result(f"Page Load: {name}", False, f"Page at {path} returned status {response.status_code}")
                    all_passed = False
            except requests.exceptions.RequestException as e:
                self.log_result(f"Page Load: {name}", False, f"Cannot load page {path}: {e}")
                all_passed = False
        
        return all_passed
    
    def test_api_endpoints(self) -> bool:
        """Test key API endpoints through the frontend proxy"""
        endpoints = [
            ("/api/channels", "Channels API"),
            ("/api/search", "Search API"),
            ("/api/analytics/global", "Analytics API")
        ]
        
        all_passed = True
        for path, name in endpoints:
            try:
                # Test through frontend proxy
                response = self.session.get(f"{FRONTEND_URL}{path}")
                if response.status_code == 200:
                    try:
                        # Try to parse as JSON
                        data = response.json()
                        self.log_result(f"API: {name}", True, f"API endpoint {path} returns valid JSON")
                    except json.JSONDecodeError:
                        self.log_result(f"API: {name}", False, f"API endpoint {path} doesn't return valid JSON")
                        all_passed = False
                elif response.status_code == 404:
                    # Try direct backend connection
                    try:
                        backend_response = self.session.get(f"{BACKEND_URL}{path}")
                        if backend_response.status_code == 200:
                            self.log_result(f"API: {name}", True, f"API endpoint {path} works on backend (proxy may not be configured)")
                        else:
                            self.log_result(f"API: {name}", False, f"API endpoint {path} returns {backend_response.status_code}")
                            all_passed = False
                    except requests.exceptions.RequestException:
                        self.log_result(f"API: {name}", False, f"API endpoint {path} not accessible")
                        all_passed = False
                else:
                    self.log_result(f"API: {name}", False, f"API endpoint {path} returned status {response.status_code}")
                    all_passed = False
            except requests.exceptions.RequestException as e:
                self.log_result(f"API: {name}", False, f"Cannot access API {path}: {e}")
                all_passed = False
        
        return all_passed
    
    def test_search_functionality(self) -> bool:
        """Test search functionality with a simple query"""
        try:
            # Test basic search
            search_params = {
                "q": "test",
                "limit": 10
            }
            response = self.session.get(f"{FRONTEND_URL}/api/search", params=search_params)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, list):
                        self.log_result("Search Functionality", True, f"Search returns {len(data)} results")
                        return True
                    else:
                        self.log_result("Search Functionality", False, "Search doesn't return a list")
                        return False
                except json.JSONDecodeError:
                    self.log_result("Search Functionality", False, "Search doesn't return valid JSON")
                    return False
            else:
                # Try direct backend
                backend_response = self.session.get(f"{BACKEND_URL}/api/search", params=search_params)
                if backend_response.status_code == 200:
                    self.log_result("Search Functionality", True, "Search works on backend")
                    return True
                else:
                    self.log_result("Search Functionality", False, f"Search returns status {response.status_code}")
                    return False
        except requests.exceptions.RequestException as e:
            self.log_result("Search Functionality", False, f"Search request failed: {e}")
            return False
    
    def test_analytics_data(self) -> bool:
        """Test analytics data retrieval"""
        try:
            # Test analytics endpoint
            response = self.session.get(f"{FRONTEND_URL}/api/analytics/global")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, dict) and "totalMessages" in data:
                        self.log_result("Analytics Data", True, f"Analytics returns data with {data.get('totalMessages', 0)} total messages")
                        return True
                    else:
                        self.log_result("Analytics Data", False, "Analytics data doesn't have expected structure")
                        return False
                except json.JSONDecodeError:
                    self.log_result("Analytics Data", False, "Analytics doesn't return valid JSON")
                    return False
            else:
                # Try direct backend
                backend_response = self.session.get(f"{BACKEND_URL}/api/analytics/global")
                if backend_response.status_code == 200:
                    self.log_result("Analytics Data", True, "Analytics works on backend")
                    return True
                else:
                    self.log_result("Analytics Data", False, f"Analytics returns status {response.status_code}")
                    return False
        except requests.exceptions.RequestException as e:
            self.log_result("Analytics Data", False, f"Analytics request failed: {e}")
            return False
    
    def run_all_tests(self) -> bool:
        """Run all tests and return overall success"""
        print("🧪 Testing RustLog SvelteKit Frontend")
        print("=" * 50)
        
        # Test backend connection first
        if not self.test_backend_connection():
            print("\n❌ Backend is not accessible. Please ensure the RustLog backend is running on localhost:8025")
            return False
        
        # Test frontend pages
        print("\n📄 Testing Frontend Pages...")
        frontend_ok = self.test_frontend_pages()
        
        # Test API endpoints
        print("\n🔌 Testing API Endpoints...")
        api_ok = self.test_api_endpoints()
        
        # Test specific functionality
        print("\n🔍 Testing Search Functionality...")
        search_ok = self.test_search_functionality()
        
        print("\n📊 Testing Analytics Data...")
        analytics_ok = self.test_analytics_data()
        
        # Summary
        print("\n" + "=" * 50)
        print("📋 Test Summary")
        print("=" * 50)
        
        passed = sum(1 for result in self.results if result["success"])
        total = len(self.results)
        
        for result in self.results:
            status = "✅" if result["success"] else "❌"
            print(f"{status} {result['test']}")
        
        print(f"\n📊 Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! The frontend is working correctly.")
            return True
        else:
            print("⚠️  Some tests failed. Please check the issues above.")
            return False

def main():
    """Main function"""
    tester = FrontendTester()
    
    try:
        success = tester.run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n⏹️  Testing interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n\n💥 Unexpected error during testing: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
